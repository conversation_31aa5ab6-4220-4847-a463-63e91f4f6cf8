<?php
/**
 * Guest Loan Application Form Component
 * 
 * This component provides a loan application form for guest users
 */

// Prevent direct access
if (!defined('LENDSWIFT')) {
    die('Direct access to this file is not allowed.');
}

// Get database connection
$db = getDbConnection();

// Initialize variables
$loan_products = [];
$currencies = [];
$default_form = null;
$dynamic_fields = [];
$errors = [];
$success_message = '';

// Get loan products (check if status column exists first)
$status_column_check = $db->query("SHOW COLUMNS FROM loan_products LIKE 'status'");
if ($status_column_check && $status_column_check->num_rows > 0) {
    $result = $db->query("SELECT id, name, min_amount, max_amount, interest_rate, term_months FROM loan_products WHERE status = 'active' ORDER BY name ASC");
} else {
    $result = $db->query("SELECT id, name, min_amount, max_amount, interest_rate, term_months FROM loan_products ORDER BY name ASC");
}
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $loan_products[] = $row;
    }
}

// Get currencies
$result = $db->query("SELECT id, name, code, symbol FROM currencies ORDER BY name ASC");
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $currencies[] = $row;
    }
}

// Check if form_definitions table exists, if not use static fields
$table_check = $db->query("SHOW TABLES LIKE 'form_definitions'");
if ($table_check && $table_check->num_rows > 0) {
    // Get default form and dynamic fields
    $result = $db->query("SELECT * FROM form_definitions WHERE form_name = 'Default Loan Application' LIMIT 1");
    if ($result && $result->num_rows > 0) {
        $default_form = $result->fetch_assoc();

        // Get form fields
        $stmt = $db->prepare("
            SELECT id, label, name, type, placeholder, help_text, options, is_required, display_order
            FROM form_fields
            WHERE form_id = ?
            ORDER BY display_order ASC
        ");
        $stmt->bind_param("i", $default_form['id']);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result) {
            while ($row = $result->fetch_assoc()) {
                // Skip loan purpose fields as we have a hardcoded one
                if (strtolower($row['label']) !== 'loan purpose' && strtolower($row['name']) !== 'loan_purpose') {
                    $dynamic_fields[] = $row;
                }
            }
        }
    }
} else {
    // Use static form fields if form_definitions doesn't exist
    $dynamic_fields = [
        [
            'id' => 1,
            'label' => 'Loan Amount',
            'name' => 'loan_amount',
            'type' => 'number',
            'placeholder' => 'Enter loan amount',
            'help_text' => 'Amount you wish to borrow',
            'options' => '',
            'is_required' => 1,
            'display_order' => 1
        ],
        [
            'id' => 2,
            'label' => 'Monthly Income',
            'name' => 'monthly_income',
            'type' => 'number',
            'placeholder' => 'Enter your monthly income',
            'help_text' => 'Your gross monthly income',
            'options' => '',
            'is_required' => 1,
            'display_order' => 2
        ],
        [
            'id' => 3,
            'label' => 'Employment Status',
            'name' => 'employment_status',
            'type' => 'select',
            'placeholder' => '',
            'help_text' => 'Your current employment status',
            'options' => 'Employed Full-time,Employed Part-time,Self-employed,Unemployed,Student,Retired',
            'is_required' => 1,
            'display_order' => 3
        ],
        [
            'id' => 4,
            'label' => 'ID Document',
            'name' => 'id_document',
            'type' => 'file',
            'placeholder' => '',
            'help_text' => 'Upload a copy of your government-issued ID',
            'options' => '',
            'is_required' => 1,
            'display_order' => 4
        ]
    ];
    $default_form = ['id' => 1]; // Default form ID
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['guest_apply'])) {
    // Get form data
    $guest_name = trim($_POST['guest_name'] ?? '');
    $guest_email = trim($_POST['guest_email'] ?? '');
    $guest_phone = trim($_POST['guest_phone'] ?? '');
    $loan_product_id = (int)($_POST['loan_product'] ?? 0);
    $loan_purpose = trim($_POST['loan_purpose'] ?? '');
    $currency_id = (int)($_POST['currency_id'] ?? 1);
    
    // Find loan amount from dynamic fields
    $loan_amount = 0;
    foreach ($dynamic_fields as $field) {
        if (strtolower($field['label']) === 'loan amount') {
            $field_name = 'field_' . $field['id'];
            $loan_amount = (float)($_POST[$field_name] ?? 0);
            break;
        }
    }
    
    // Validate form data
    if (empty($guest_name)) {
        $errors[] = 'Full name is required.';
    }
    
    if (empty($guest_email)) {
        $errors[] = 'Email address is required.';
    } elseif (!filter_var($guest_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    } else {
        // Check if email already exists as a user
        $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->bind_param("s", $guest_email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $errors[] = 'An account with this email already exists. Please <a href="' . BASE_URL . '/?page=login">login</a> to apply.';
        }
    }
    
    if (empty($loan_product_id)) {
        $errors[] = 'Please select a loan product.';
    }
    
    if (empty($loan_amount) || $loan_amount <= 0) {
        $errors[] = 'Please enter a valid loan amount.';
    }
    
    if (empty($loan_purpose)) {
        $errors[] = 'Please select a loan purpose.';
    }
    
    // Validate dynamic fields
    foreach ($dynamic_fields as $field) {
        $field_name = 'field_' . $field['id'];
        $field_value = $_POST[$field_name] ?? '';
        
        if ($field['is_required'] && empty($field_value) && $field['type'] !== 'file') {
            $errors[] = $field['label'] . ' is required.';
        }
        
        // Validate file uploads
        if ($field['type'] === 'file' && $field['is_required']) {
            if (!isset($_FILES[$field_name]) || $_FILES[$field_name]['error'] !== UPLOAD_ERR_OK) {
                $errors[] = $field['label'] . ' is required.';
            }
        }
    }
    
    // If no validation errors, submit the application
    if (empty($errors)) {
        try {
            // Start transaction
            $db->begin_transaction();
            
            // Get form ID
            $form_id = isset($default_form['id']) ? $default_form['id'] : 1;
            
            // Insert guest loan application
            $stmt = $db->prepare("
                INSERT INTO loan_applications (
                    user_id, guest_name, guest_email, guest_phone, 
                    loan_product_id, applied_amount, currency_id,
                    purpose, status_id, submission_date, form_id, is_guest_application
                ) VALUES (
                    NULL, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), ?, TRUE
                )
            ");
            
            $stmt->bind_param("sssiidisi",
                $guest_name,
                $guest_email,
                $guest_phone,
                $loan_product_id,
                $loan_amount,
                $currency_id,
                $loan_purpose,
                $form_id
            );
            
            if (!$stmt->execute()) {
                throw new Exception("Failed to insert guest loan application: " . $db->error);
            }
            
            // Get the application ID
            $application_id = $db->insert_id;
            
            // Handle file uploads
            $upload_dir = UPLOADS_PATH . '/documents';
            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            // Process file uploads
            foreach ($dynamic_fields as $field) {
                if ($field['type'] === 'file' && isset($_FILES['field_' . $field['id']])) {
                    $field_name = 'field_' . $field['id'];
                    $file = $_FILES[$field_name];

                    if ($file['error'] === UPLOAD_ERR_OK) {
                        // Generate unique filename for guest
                        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                        $new_filename = 'guest_' . strtolower(str_replace(' ', '_', $field['label'])) . '_' . $application_id . '_' . time() . '.' . $file_extension;
                        $upload_path = $upload_dir . '/' . $new_filename;

                        if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                            // Insert document record
                            $relative_path = 'uploads/documents/' . $new_filename;
                            $stmt = $db->prepare("
                                INSERT INTO application_documents (
                                    application_id, user_id, guest_email, file_name, file_path, upload_date, status
                                ) VALUES (
                                    ?, NULL, ?, ?, ?, NOW(), 'pending'
                                )
                            ");
                            $stmt->bind_param("isss", $application_id, $guest_email, $file['name'], $relative_path);
                            $stmt->execute();
                        }
                    }
                }
            }

            // Insert form data for non-file fields
            foreach ($dynamic_fields as $field) {
                if ($field['type'] === 'file') {
                    continue;
                }

                $field_name = 'field_' . $field['id'];
                $field_value = $_POST[$field_name] ?? '';

                // Skip empty non-required fields
                if (empty($field_value) && !$field['is_required']) {
                    continue;
                }

                // Handle array values (checkboxes)
                if (is_array($field_value)) {
                    $field_value = implode(', ', $field_value);
                }

                // Insert form data
                $stmt = $db->prepare("
                    INSERT INTO form_data (
                        application_id, field_id, user_id, guest_email, value
                    ) VALUES (
                        ?, ?, NULL, ?, ?
                    )
                ");

                $stmt->bind_param("iiss",
                    $application_id,
                    $field['id'],
                    $guest_email,
                    $field_value
                );

                $stmt->execute();
            }

            // Send notification email to admin (optional)
            // TODO: Implement admin notification

            // Commit transaction
            $db->commit();
            
            $success_message = "Your loan application has been submitted successfully! We will review your application and contact you within 24-48 hours.";
            
            // Clear form data on success
            $_POST = [];
            
        } catch (Exception $e) {
            // Rollback transaction
            $db->rollback();
            error_log('Guest Loan Application Error: ' . $e->getMessage());
            $errors[] = 'An error occurred while submitting your application. Please try again later.';
        }
    }
}

// Function to render form field
function render_guest_form_field($field, $value = '') {
    $field_name = 'field_' . $field['id'];
    $required = $field['is_required'] ? 'required' : '';
    $required_star = $field['is_required'] ? '<span class="required">*</span>' : '';

    $html = '<div class="form-group">';
    $html .= '<label for="' . $field_name . '">' . htmlspecialchars($field['label']) . ' ' . $required_star . '</label>';

    switch ($field['type']) {
        case 'text':
        case 'email':
        case 'tel':
        case 'number':
            $html .= '<input type="' . $field['type'] . '" id="' . $field_name . '" name="' . $field_name . '" class="form-control" ';
            $html .= 'value="' . htmlspecialchars($value) . '" ';
            if (!empty($field['placeholder'])) {
                $html .= 'placeholder="' . htmlspecialchars($field['placeholder']) . '" ';
            }
            $html .= $required . '>';
            break;

        case 'textarea':
            $html .= '<textarea id="' . $field_name . '" name="' . $field_name . '" class="form-control" rows="4" ';
            if (!empty($field['placeholder'])) {
                $html .= 'placeholder="' . htmlspecialchars($field['placeholder']) . '" ';
            }
            $html .= $required . '>' . htmlspecialchars($value) . '</textarea>';
            break;

        case 'select':
            $html .= '<select id="' . $field_name . '" name="' . $field_name . '" class="form-control" ' . $required . '>';
            $html .= '<option value="">Select ' . htmlspecialchars($field['label']) . '</option>';
            if (!empty($field['options'])) {
                $options = explode(',', $field['options']);
                foreach ($options as $option) {
                    $option = trim($option);
                    $selected = ($value === $option) ? 'selected' : '';
                    $html .= '<option value="' . htmlspecialchars($option) . '" ' . $selected . '>' . htmlspecialchars($option) . '</option>';
                }
            }
            $html .= '</select>';
            break;

        case 'file':
            $html .= '<input type="file" id="' . $field_name . '" name="' . $field_name . '" class="form-control" ';
            $html .= 'accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" ' . $required . '>';
            $html .= '<small class="form-text text-muted">Accepted formats: PDF, JPG, PNG, DOC, DOCX (Max 5MB)</small>';
            break;

        case 'checkbox':
            if (!empty($field['options'])) {
                $options = explode(',', $field['options']);
                $selected_values = is_array($value) ? $value : explode(', ', $value);
                foreach ($options as $option) {
                    $option = trim($option);
                    $checked = in_array($option, $selected_values) ? 'checked' : '';
                    $html .= '<div class="form-check">';
                    $html .= '<input type="checkbox" id="' . $field_name . '_' . md5($option) . '" name="' . $field_name . '[]" ';
                    $html .= 'value="' . htmlspecialchars($option) . '" class="form-check-input" ' . $checked . '>';
                    $html .= '<label class="form-check-label" for="' . $field_name . '_' . md5($option) . '">' . htmlspecialchars($option) . '</label>';
                    $html .= '</div>';
                }
            }
            break;

        case 'radio':
            if (!empty($field['options'])) {
                $options = explode(',', $field['options']);
                foreach ($options as $option) {
                    $option = trim($option);
                    $checked = ($value === $option) ? 'checked' : '';
                    $html .= '<div class="form-check">';
                    $html .= '<input type="radio" id="' . $field_name . '_' . md5($option) . '" name="' . $field_name . '" ';
                    $html .= 'value="' . htmlspecialchars($option) . '" class="form-check-input" ' . $checked . ' ' . $required . '>';
                    $html .= '<label class="form-check-label" for="' . $field_name . '_' . md5($option) . '">' . htmlspecialchars($option) . '</label>';
                    $html .= '</div>';
                }
            }
            break;
    }

    if (!empty($field['help_text'])) {
        $html .= '<small class="form-text text-muted">' . htmlspecialchars($field['help_text']) . '</small>';
    }

    $html .= '</div>';
    return $html;
}
?>
