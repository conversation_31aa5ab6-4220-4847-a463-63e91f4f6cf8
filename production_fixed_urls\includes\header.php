<?php
/**
 * Header Template
 *
 * This file contains the header template for the site.
 *
 * @package LendSwift
 */

// Prevent direct access to this file
if (!defined('LENDSWIFT')) {
    die('Direct access to this file is not allowed.');
}

// Get current page
$current_page = $_GET['page'] ?? 'home';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo ucfirst($current_page); ?> - <?php echo SITE_NAME; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo ASSETS_URL; ?>/images/favicon.svg">

    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/styles.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/mobile-fix.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/logo-fix.css">

    <?php
    // Include additional CSS files
    global $styles;
    if (isset($styles) && is_array($styles)) {
        foreach ($styles as $style) {
            echo '<link rel="stylesheet" href="' . ASSETS_URL . '/css/' . $style . '">' . PHP_EOL;
        }
    }
    ?>

    <!-- Font Awesome (for icons) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --primary-light: #e0e7ff;
            --text-color: #1f2937;
            --text-muted: #6b7280;
            --border-color: #e5e7eb;
            --background-color: #f9fafb;
            --card-background: #ffffff;
            --success-color: #10b981;
            --error-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
        }

        /* Add smooth scrolling to the entire website */
        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: var(--text-color);
            line-height: 1.5;
            margin: 0;
            padding: 0;
            background-color: var(--background-color);
        }

        .header {
            background-color: var(--card-background);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
        }

        .logo img {
            height: 70px;
            width: auto;
        }

        /* Logo size classes */
        .logo img.logo-small {
            height: 50px;
        }

        .logo img.logo-medium {
            height: 70px;
        }

        .logo img.logo-large {
            height: 90px;
        }

        /* Custom logo size using CSS variables */
        .logo img {
            height: calc(70px * var(--logo-size-factor, 1));
        }

        .logo-text {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-color);
            margin-left: 0.5rem;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-item {
            margin-left: 1.5rem;
        }

        .nav-link {
            color: var(--text-color);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: color 0.2s ease;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-link.active {
            color: var(--primary-color);
        }

        .auth-buttons {
            display: flex;
            align-items: center;
        }

        .button {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .button-primary {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }

        .button-primary:hover {
            background-color: var(--primary-hover);
        }

        .button-secondary {
            background-color: white;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
            margin-right: 0.75rem;
        }

        .button-secondary:hover {
            background-color: var(--primary-light);
        }

        .user-menu {
            position: relative;
        }

        .user-menu-button {
            display: flex;
            align-items: center;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            color: var(--text-color);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-light);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 0.5rem;
        }

        .user-name {
            font-size: 0.875rem;
            font-weight: 500;
            margin-right: 0.25rem;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: var(--card-background);
            border-radius: 0.375rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 0.5rem 0;
            min-width: 200px;
            z-index: 10;
            display: none;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            padding: 0.5rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            font-size: 0.875rem;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: var(--background-color);
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 0.5rem 0;
        }

        .mobile-menu-button {
            display: none;
            background: none;
            border: none;
            color: var(--text-color);
            font-size: 1.5rem;
            cursor: pointer;
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
            font-size: 0.875rem;
        }

        .alert-danger {
            background-color: #fee2e2;
            color: #b91c1c;
            border: 1px solid #fecaca;
        }

        .alert-success {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-warning {
            background-color: #fff7ed;
            color: #c2410c;
            border: 1px solid #fed7aa;
        }

        .alert-info {
            background-color: #eff6ff;
            color: #1e40af;
            border: 1px solid #bfdbfe;
        }

        .main-content {
            padding: 2rem 0;
        }

        /* Desktop and Mobile Specific Styles */
        .desktop-only {
            display: block;
        }

        .mobile-only {
            display: none;
        }

        /* Mobile Menu Toggle Button */
        .mobile-menu-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 1.25rem;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 1010;
        }

        /* Off-Canvas Menu Overlay */
        .mobile-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1005;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease;
            display: none;
        }

        /* Pop-up Mobile Menu */
        .mobile-menu {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            width: 90%;
            max-width: 320px;
            max-height: 80vh;
            background-color: white;
            z-index: 1010;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease, opacity 0.3s ease;
            padding: 1.5rem;
            overflow-y: auto;
            display: none;
            opacity: 0;
            border-radius: 12px;
        }

        .mobile-menu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            position: relative;
        }

        .mobile-menu-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-muted);
            cursor: pointer;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s ease;
        }

        .mobile-menu-close:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .mobile-menu-items {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .mobile-menu-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 0;
            text-decoration: none;
            color: var(--text-color);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .mobile-menu-item i {
            width: 30px;
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .mobile-menu-item.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        /* Show mobile menu when active */
        .mobile-menu.active {
            display: block;
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .mobile-menu-overlay.active {
            opacity: 1;
            visibility: visible;
            display: block;
        }

        /* Media Query for Mobile Devices */
        @media (max-width: 768px) {
            .desktop-only {
                display: none !important;
            }

            .mobile-only {
                display: block !important;
            }
        }
    </style>
</head>
<body>
    <!-- Desktop Header - Only visible on desktop -->
    <header class="header desktop-only">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="<?php echo BASE_URL; ?>/">
                        <?php
                        $site_logo = get_setting('site_logo', '/assets/images/logo.svg');
                        $logo_size = get_setting('logo_size', 'medium');
                        $logo_size_class = 'logo-' . $logo_size;
                        $logo_custom_size = get_setting('logo_custom_size', '100');
                        $show_site_name = get_setting('show_site_name', '0');

                        // Calculate custom size factor (percentage to decimal)
                        $size_factor = intval($logo_custom_size) / 100;

                        if (!empty($site_logo) && file_exists(BASE_PATH . $site_logo)) {
                            // If logo exists, show it with custom size
                            echo '<img src="' . BASE_URL . $site_logo . '" alt="' . SITE_NAME . '" class="' . $logo_size_class . '" style="--logo-size-factor: ' . $size_factor . ';">';

                            // Show site name if enabled
                            if ($show_site_name == '1') {
                                echo '<span class="logo-text">' . SITE_NAME . '</span>';
                            }
                        } else {
                            // If no logo, show site name
                            echo '<span class="logo-text">' . SITE_NAME . '</span>';
                        }
                        ?>
                    </a>
                </div>

                <div class="header-right">
                    <nav>
                        <ul class="nav-menu" id="nav-menu">
                            <li class="nav-item">
                                <a href="<?php echo BASE_URL; ?>/" class="nav-link <?php echo $current_page === 'home' ? 'active' : ''; ?>">Home</a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo BASE_URL; ?>/?page=about" class="nav-link <?php echo $current_page === 'about' ? 'active' : ''; ?>">About</a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo BASE_URL; ?>/?page=loan-products" class="nav-link <?php echo $current_page === 'loan-products' ? 'active' : ''; ?>">Loan Products</a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo BASE_URL; ?>/?page=services" class="nav-link <?php echo $current_page === 'services' ? 'active' : ''; ?>">Services</a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo BASE_URL; ?>/?page=apply" class="nav-link <?php echo $current_page === 'apply' ? 'active' : ''; ?>">Apply</a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo BASE_URL; ?>/?page=privacy" class="nav-link <?php echo $current_page === 'privacy' ? 'active' : ''; ?>">Privacy</a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo BASE_URL; ?>/?page=terms" class="nav-link <?php echo $current_page === 'terms' ? 'active' : ''; ?>">Terms</a>
                            </li>
                            <li class="nav-item">
                                <a href="<?php echo BASE_URL; ?>/?page=contact" class="nav-link <?php echo $current_page === 'contact' ? 'active' : ''; ?>">Contact</a>
                            </li>
                        </ul>
                    </nav>

                    <div class="auth-buttons">
                    <?php if (is_user_logged_in()): ?>
                        <div class="user-menu">
                            <button class="user-menu-button" id="user-menu-button">
                                <div class="user-avatar">
                                    <?php echo substr($_SESSION['user_name'], 0, 1); ?>
                                </div>
                                <span class="user-name"><?php echo $_SESSION['user_name']; ?></span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu" id="user-dropdown-menu">
                                <a href="<?php echo BASE_URL; ?>/?page=dashboard" class="dropdown-item">
                                    <i class="fas fa-tachometer-alt"></i> Dashboard
                                </a>
                                <a href="<?php echo BASE_URL; ?>/?page=profile" class="dropdown-item">
                                    <i class="fas fa-user"></i> Profile
                                </a>
                                <a href="<?php echo BASE_URL; ?>/?page=loans" class="dropdown-item">
                                    <i class="fas fa-file-invoice-dollar"></i> My Loans
                                </a>
                                <a href="<?php echo BASE_URL; ?>/?page=documents" class="dropdown-item">
                                    <i class="fas fa-file-alt"></i> Documents
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="<?php echo BASE_URL; ?>/?page=logout" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo BASE_URL; ?>/?page=login" class="button button-primary">Login</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Header - Only visible on mobile -->
    <header class="header mobile-only">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="<?php echo BASE_URL; ?>/">
                        <img src="<?php echo BASE_URL . $site_logo; ?>" alt="<?php echo SITE_NAME; ?>">
                    </a>
                    <span class="logo-text"><?php echo SITE_NAME; ?></span>
                </div>

                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Mobile Off-Canvas Menu - Only for mobile -->
    <div class="mobile-menu-overlay mobile-only" id="mobile-menu-overlay"></div>
    <div class="mobile-menu mobile-only" id="mobile-menu">
        <div class="mobile-menu-header">
            <div class="logo">
                <img src="<?php echo BASE_URL . $site_logo; ?>" alt="<?php echo SITE_NAME; ?>" style="height: 30px;">
                <span class="logo-text" style="font-size: 1rem;"><?php echo SITE_NAME; ?></span>
            </div>
            <button class="mobile-menu-close" id="mobile-menu-close">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="mobile-menu-items">
            <a href="<?php echo BASE_URL; ?>/" class="mobile-menu-item <?php echo $current_page === 'home' ? 'active' : ''; ?>">
                <i class="fas fa-home"></i> Home
            </a>
            <a href="<?php echo BASE_URL; ?>/?page=about" class="mobile-menu-item <?php echo $current_page === 'about' ? 'active' : ''; ?>">
                <i class="fas fa-info-circle"></i> About
            </a>
            <a href="<?php echo BASE_URL; ?>/?page=loan-products" class="mobile-menu-item <?php echo $current_page === 'loan-products' ? 'active' : ''; ?>">
                <i class="fas fa-money-bill-wave"></i> Loan Products
            </a>
            <a href="<?php echo BASE_URL; ?>/?page=services" class="mobile-menu-item <?php echo $current_page === 'services' ? 'active' : ''; ?>">
                <i class="fas fa-concierge-bell"></i> Services
            </a>
            <a href="<?php echo BASE_URL; ?>/?page=apply" class="mobile-menu-item <?php echo $current_page === 'apply' ? 'active' : ''; ?>">
                <i class="fas fa-file-alt"></i> Apply
            </a>
            <a href="<?php echo BASE_URL; ?>/?page=privacy" class="mobile-menu-item <?php echo $current_page === 'privacy' ? 'active' : ''; ?>">
                <i class="fas fa-shield-alt"></i> Privacy
            </a>
            <a href="<?php echo BASE_URL; ?>/?page=terms" class="mobile-menu-item <?php echo $current_page === 'terms' ? 'active' : ''; ?>">
                <i class="fas fa-file-contract"></i> Terms
            </a>
            <a href="<?php echo BASE_URL; ?>/?page=contact" class="mobile-menu-item <?php echo $current_page === 'contact' ? 'active' : ''; ?>">
                <i class="fas fa-envelope"></i> Contact
            </a>

            <?php if (is_user_logged_in()): ?>
                <div class="dropdown-divider"></div>
                <a href="<?php echo BASE_URL; ?>/?page=dashboard" class="mobile-menu-item <?php echo $current_page === 'dashboard' ? 'active' : ''; ?>">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="<?php echo BASE_URL; ?>/?page=profile" class="mobile-menu-item <?php echo $current_page === 'profile' ? 'active' : ''; ?>">
                    <i class="fas fa-user"></i> Profile
                </a>
                <a href="<?php echo BASE_URL; ?>/?page=loans" class="mobile-menu-item <?php echo $current_page === 'loans' ? 'active' : ''; ?>">
                    <i class="fas fa-file-invoice-dollar"></i> My Loans
                </a>
                <a href="<?php echo BASE_URL; ?>/?page=documents" class="mobile-menu-item <?php echo $current_page === 'documents' ? 'active' : ''; ?>">
                    <i class="fas fa-file-alt"></i> Documents
                </a>
                <div class="dropdown-divider"></div>
                <a href="<?php echo BASE_URL; ?>/?page=logout" class="mobile-menu-item">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            <?php else: ?>
                <div class="dropdown-divider"></div>
                <a href="<?php echo BASE_URL; ?>/?page=login" class="mobile-menu-item">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="container">
        <div class="main-content">
            <?php echo display_flash_message(); ?>