-- Simple Guest Application Migration
-- Run this in phpMyAdmin or MySQL command line when <PERSON><PERSON> is running

USE loan;

-- Step 1: Make user_id nullable in loan_applications
ALTER TABLE loan_applications MODIFY COLUMN user_id INT NULL;

-- Step 2: Add guest application fields
ALTER TABLE loan_applications 
ADD COLUMN guest_name VARCHAR(100) NULL AFTER user_id,
ADD COLUMN guest_email VARCHAR(255) NULL AFTER guest_name,
ADD COLUMN guest_phone VARCHAR(20) NULL AFTER guest_email,
ADD COLUMN is_guest_application BOOLEAN DEFAULT FALSE AFTER guest_phone;

-- Step 3: Make user_id nullable in application_documents
ALTER TABLE application_documents MODIFY COLUMN user_id INT NULL;

-- Step 4: Add guest email to application_documents
ALTER TABLE application_documents 
ADD COLUMN guest_email VARCHAR(255) NULL AFTER user_id;

-- Step 5: Make user_id nullable in form_data
ALTER TABLE form_data MODIFY COLUMN user_id INT NULL;

-- Step 6: Add guest email to form_data
ALTER TABLE form_data 
ADD COLUMN guest_email VARCHAR(255) NULL AFTER user_id;

-- Step 7: Update existing applications to mark them as non-guest
UPDATE loan_applications 
SET is_guest_application = FALSE 
WHERE user_id IS NOT NULL;

-- Step 8: Create indexes for better performance
CREATE INDEX idx_guest_applications ON loan_applications(is_guest_application, guest_email);
CREATE INDEX idx_guest_email ON loan_applications(guest_email);

-- Verification
SELECT 'Migration completed successfully' as status;
SELECT COUNT(*) as total_applications FROM loan_applications;
SELECT COUNT(*) as guest_applications FROM loan_applications WHERE is_guest_application = TRUE;
SELECT COUNT(*) as user_applications FROM loan_applications WHERE is_guest_application = FALSE;
