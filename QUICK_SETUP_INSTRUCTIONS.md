# Quick Setup Instructions for Guest Loan Application

## Issues Fixed ✅

1. **Database Error Fixed**: The `loan_products` table status column issue has been resolved
2. **Guest Application Form**: Added to home page after "How It Works" section
3. **Images Added**: Hero section now uses demo images with floating cards
4. **Background Images**: Features section has background image
5. **Admin Interface**: Updated to handle guest applications

## To Complete Setup:

### 1. Start MAMP
- Open MAMP application
- Start Apache and MySQL servers
- Note the port numbers (usually 80 for Apache, 3306 for MySQL)

### 2. Run Database Migration
**Option A: Using phpMyAdmin**
1. Open http://localhost/phpMyAdmin (or http://localhost:8888/phpMyAdmin)
2. Select the `loan` database
3. Go to SQL tab
4. Copy and paste the contents of `SIMPLE_MIGRATION.sql`
5. Click "Go" to execute

**Option B: Using MySQL Command Line**
```bash
mysql -u root -p loan < SIMPLE_MIGRATION.sql
```

### 3. Test the Website
1. Open http://localhost/pfloans.com (or http://localhost:8888/pfloans.com)
2. Scroll down to see the "Apply for a Loan in Minutes" section
3. Try filling out the guest application form

### 4. Test Admin Interface
1. Login to admin panel
2. Go to Applications section
3. Guest applications will show "Guest" badge
4. When you approve a guest application, it automatically creates a user account

## What's New:

### Guest Application Form Features:
- **Personal Information**: Name, email, phone, currency selection
- **Loan Information**: Product selection, loan purpose
- **Dynamic Fields**: Based on admin form configuration
- **Document Upload**: Supports file uploads for guest applications
- **Terms Agreement**: Required checkbox

### Admin Features:
- **Guest Badge**: Shows "Guest" indicator in applications list
- **Account Creation**: Automatic when guest application is approved
- **Email Notifications**: Sends login credentials to approved guests
- **Status Tracking**: Clear indication of guest vs registered users

### Visual Enhancements:
- **Hero Section**: Now uses demo images with floating animation cards
- **Background Images**: Features section has parallax background
- **Responsive Design**: Works on both desktop and mobile
- **Modern Styling**: Consistent with existing design

## File Structure:
```
├── assets/images/           # Demo images copied here
├── includes/components/     # Guest form components
├── sql/                     # Migration scripts
├── SIMPLE_MIGRATION.sql     # Quick migration script
└── QUICK_SETUP_INSTRUCTIONS.md # This file
```

## Troubleshooting:

### If Guest Form Doesn't Show:
1. Check if MAMP is running
2. Verify database connection in `includes/core/config.php`
3. Check for PHP errors in browser console

### If Database Migration Fails:
1. Make sure you're using the correct database name (`loan`)
2. Check if tables exist: `loan_applications`, `application_documents`, `form_data`
3. Verify MySQL user has ALTER privileges

### If Images Don't Load:
1. Check if images exist in `assets/images/` folder
2. Verify file permissions
3. Check browser console for 404 errors

## Next Steps:
1. Start MAMP
2. Run the migration
3. Test guest application
4. Customize form fields in admin panel
5. Test email notifications

The guest loan application feature is now ready to use! 🎉
