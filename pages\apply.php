<?php
/**
 * Apply for Loan Page
 *
 * This page shows the guest loan application form for non-logged users
 * and redirects logged-in users to the dashboard loan application.
 */

// Prevent direct access
if (!defined('LENDSWIFT')) {
    die('Direct access to this file is not allowed.');
}

// Check if user is logged in
if (is_user_logged_in()) {
    // Redirect logged-in users to the regular loan application page
    redirect(BASE_URL . '/?page=loan-application');
    exit;
}

// Include the guest loan application form logic
include_once INCLUDES_PATH . '/components/guest_loan_form.php';
?>

<div class="apply-page-wrapper">
    <div class="apply-hero-section">
        <div class="container">
            <div class="apply-hero-content">
                <h1>Apply for Your Loan</h1>
                <p>Get started with your loan application. No account required - we'll create one for you upon approval.</p>
                <div class="apply-benefits">
                    <div class="benefit-item">
                        <i class="fas fa-clock"></i>
                        <span>Quick 5-minute application</span>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>Secure & confidential</span>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-check-circle"></i>
                        <span>Instant pre-approval</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php
    // Include the guest loan application form HTML
    include_once INCLUDES_PATH . '/components/guest_loan_form_html.php';
    ?>
</div>

<style>
    .apply-page-wrapper {
        width: 100%;
        min-height: 100vh;
    }

    .apply-hero-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        color: white;
        padding: 4rem 0 2rem;
        text-align: center;
    }

    .apply-hero-content h1 {
        font-size: 3rem;
        font-weight: 800;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .apply-hero-content p {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .apply-benefits {
        display: flex;
        justify-content: center;
        gap: 3rem;
        flex-wrap: wrap;
        margin-top: 2rem;
    }

    .apply-benefits .benefit-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: white;
        font-weight: 500;
    }

    .apply-benefits .benefit-item i {
        font-size: 1.25rem;
        opacity: 0.9;
    }

    /* Override guest form section styling for apply page */
    .guest-application-section {
        padding: 0;
        background: #f8fafc;
    }

    .guest-application-container {
        margin: -2rem auto 0;
        position: relative;
        z-index: 10;
    }

    @media (max-width: 768px) {
        .apply-hero-content h1 {
            font-size: 2rem;
        }

        .apply-hero-content p {
            font-size: 1rem;
        }

        .apply-benefits {
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }

        .guest-application-container {
            margin: -1rem auto 0;
        }
    }
</style>
