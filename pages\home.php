<?php
/**
 * Home Page
 *
 * This file contains the home page content.
 *
 * @package Pharaoh Finance Private and Fast Loans
 */

// Define LENDSWIFT constant to prevent direct access to included files
if (!defined('LENDSWIFT')) {
    define('LENDSWIFT', true);
}

// Include initialization file
if (!defined('LENDSWIFT')) {
    // If accessed directly, use relative path
    require_once '../includes/init.php';
}
?>

<div class="home-wrapper">
    <!-- Mobile App Navigation (visible only on small screens) -->
    <div class="mobile-app-nav">
        <a href="<?php echo BASE_URL; ?>/" class="mobile-nav-item active">
            <div class="nav-icon-container">
                <i class="fas fa-home"></i>
            </div>
            <span>Home</span>
        </a>
        <a href="<?php echo BASE_URL; ?>/?page=loan-products" class="mobile-nav-item">
            <div class="nav-icon-container">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <span>Loans</span>
        </a>
        <a href="<?php echo BASE_URL; ?>/?page=login" class="mobile-nav-item">
            <div class="nav-icon-container">
                <i class="fas fa-calculator"></i>
            </div>
            <span>Calculate</span>
        </a>
        <a href="<?php echo BASE_URL; ?>/?page=contact" class="mobile-nav-item">
            <div class="nav-icon-container">
                <i class="fas fa-headset"></i>
            </div>
            <span>Support</span>
        </a>
        <a href="<?php echo BASE_URL; ?>/?page=login" class="mobile-nav-item">
            <div class="nav-icon-container">
                <i class="fas fa-user"></i>
            </div>
            <span>Account</span>
        </a>
    </div>
    <!-- Desktop Hero Section - Hidden on Mobile -->
    <section class="hero-section section-light desktop-only">
        <div class="section-container">
            <div class="hero-content">
                <span class="hero-badge">Fast & Secure Loans</span>
                <h1>Financial Solutions <span class="text-gradient">Tailored for You</span></h1>
                <p class="hero-description">Get the funds you need with our streamlined loan application process. Low rates, flexible terms, and exceptional customer service.</p>
                <div class="hero-buttons">
                    <a href="<?php echo BASE_URL; ?>/?page=login" class="button button-primary">
                        <i class="fas fa-user-plus"></i> Get Started
                    </a>
                    <a href="<?php echo BASE_URL; ?>/?page=login" class="button button-secondary">
                        <i class="fas fa-calculator"></i> Calculate Your Loan
                    </a>
                </div>
                <div class="hero-stats">
                    <div class="hero-stat">
                        <span class="stat-number">5000+</span>
                        <span class="stat-label">Happy Customers</span>
                    </div>
                    <div class="hero-stat">
                        <span class="stat-number">$10M+</span>
                        <span class="stat-label">Loans Funded</span>
                    </div>
                    <div class="hero-stat">
                        <span class="stat-number">4.9/5</span>
                        <span class="stat-label">Customer Rating</span>
                    </div>
                </div>
            </div>
            <div class="hero-image">
                <img src="<?php echo BASE_URL; ?>/assets/images/hero-image.webp" alt="Financial Solutions" style="border-radius: 12px; box-shadow: 0 20px 40px rgba(0,0,0,0.1);">
                <div class="hero-shape-1">
                    <div class="floating-card">
                        <i class="fas fa-chart-line"></i>
                        <span>Fast Approval</span>
                    </div>
                </div>
                <div class="hero-shape-2">
                    <div class="floating-card">
                        <i class="fas fa-shield-alt"></i>
                        <span>Secure Process</span>
                    </div>
                </div>
                <div class="loan-icon loan-icon-1">
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="20" cy="20" r="20" fill="#E0E7FF"/>
                        <path d="M15 20C15 17.2386 17.2386 15 20 15C22.7614 15 25 17.2386 25 20C25 22.7614 22.7614 25 20 25C17.2386 25 15 22.7614 15 20Z" stroke="#4F46E5" stroke-width="1.5"/>
                        <path d="M20 17.5V20L22 22" stroke="#4F46E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="loan-icon loan-icon-2">
                    <svg width="35" height="35" viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="17.5" cy="17.5" r="17.5" fill="#E0E7FF"/>
                        <rect x="10" y="10" width="15" height="15" rx="2" stroke="#4F46E5" stroke-width="1.5"/>
                        <path d="M13 15H22" stroke="#4F46E5" stroke-width="1.5" stroke-linecap="round"/>
                        <path d="M13 19H22" stroke="#4F46E5" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                </div>
                <div class="loan-icon loan-icon-3">
                    <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="15" cy="15" r="15" fill="#E0E7FF"/>
                        <path d="M10 12.5H20V20H10V12.5Z" stroke="#4F46E5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M12.5 10V12.5" stroke="#4F46E5" stroke-width="1.5" stroke-linecap="round"/>
                        <path d="M17.5 10V12.5" stroke="#4F46E5" stroke-width="1.5" stroke-linecap="round"/>
                        <path d="M10 15H20" stroke="#4F46E5" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                </div>
                <div class="loan-icon loan-icon-4">
                    <svg width="45" height="45" viewBox="0 0 45 45" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="22.5" cy="22.5" r="22.5" fill="#E0E7FF"/>
                        <path d="M15 22.5C15 18.3579 18.3579 15 22.5 15C26.6421 15 30 18.3579 30 22.5C30 26.6421 26.6421 30 22.5 30C18.3579 30 15 26.6421 15 22.5Z" stroke="#4F46E5" stroke-width="1.5"/>
                        <text x="22.5" y="25" text-anchor="middle" font-size="10" fill="#4F46E5" font-weight="bold">$</text>
                    </svg>
                </div>
            </div>
        </div>
    </section>

    <!-- Mobile Hero Section - Only visible on mobile -->
    <section class="mobile-hero-section mobile-only">
        <div class="mobile-hero-container">
            <!-- Creative Hero Section -->
            <div class="mobile-hero-creative">
                <div class="mobile-hero-icon-wrapper">
                    <div class="mobile-hero-icon-bg">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
                <div class="mobile-hero-text">
                    <h1 class="mobile-hero-title"><span style="color: var(--primary-color);">Financial Solutions</span><br><span style="color: var(--text-color);">Tailored for You</span></h1>
                    <p class="mobile-hero-subtitle">Fast & Secure Loans</p>
                </div>
                <a href="<?php echo BASE_URL; ?>/?page=login" class="mobile-hero-cta-button">
                    Get Started <i class="fas fa-arrow-right"></i>
                </a>
            </div>

            <!-- Stats Cards -->
            <div class="mobile-stats-row">
                <div class="mobile-stat-card">
                    <div class="mobile-stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="mobile-stat-number">5000+</div>
                    <div class="mobile-stat-label">Customers</div>
                </div>
                <div class="mobile-stat-card">
                    <div class="mobile-stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="mobile-stat-number">$10M+</div>
                    <div class="mobile-stat-label">Funded</div>
                </div>
                <div class="mobile-stat-card">
                    <div class="mobile-stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="mobile-stat-number">4.9/5</div>
                    <div class="mobile-stat-label">Rating</div>
                </div>
            </div>

            <!-- Loan Options -->
            <div class="mobile-loan-options">
                <h3>Loan Options</h3>
                <div class="mobile-loan-cards">
                    <a href="<?php echo BASE_URL; ?>/?page=login" class="mobile-loan-card">
                        <div class="mobile-loan-card-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="mobile-loan-card-content">
                            <h4>Personal Loan</h4>
                            <p>From $1,000 - $10,000</p>
                        </div>
                        <div class="mobile-loan-card-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                    <a href="<?php echo BASE_URL; ?>/?page=login" class="mobile-loan-card">
                        <div class="mobile-loan-card-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="mobile-loan-card-content">
                            <h4>Business Loan</h4>
                            <p>From $5,000 - $50,000</p>
                        </div>
                        <div class="mobile-loan-card-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mobile-quick-actions">
                <a href="<?php echo BASE_URL; ?>/?page=login" class="quick-action-button">
                    <i class="fas fa-calculator"></i>
                    <span>Calculate</span>
                </a>
                <a href="<?php echo BASE_URL; ?>/?page=login" class="quick-action-button">
                    <i class="fas fa-user-plus"></i>
                    <span>Register</span>
                </a>
                <a href="<?php echo BASE_URL; ?>/?page=contact" class="quick-action-button">
                    <i class="fas fa-headset"></i>
                    <span>Support</span>
                </a>
            </div>
        </div>
    </section>

    <section id="statistics" class="statistics-section section-darker">
        <div class="section-container">
            <div class="section-header">
                <span class="section-badge">Our Impact</span>
                <h2>Trusted by Thousands</h2>
                <p>See how we've helped our customers achieve their financial goals</p>
            </div>

            <div class="statistics-container">
                <div class="statistics-grid">
                    <div class="statistic-card">
                        <div class="statistic-svg">
                            <svg width="80" height="80" viewBox="0 0 200 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <!-- Background Circle -->
                                <circle cx="100" cy="60" r="50" fill="#E0E7FF" opacity="0.5"/>

                                <!-- Vertical Progress Bars -->
                                <rect x="70" y="30" width="10" height="60" rx="5" fill="#E5E7EB"/>
                                <rect x="70" y="70" width="10" height="20" rx="5" fill="#4F46E5"/>

                                <rect x="90" y="30" width="10" height="60" rx="5" fill="#E5E7EB"/>
                                <rect x="90" y="50" width="10" height="40" rx="5" fill="#4F46E5"/>

                                <rect x="110" y="30" width="10" height="60" rx="5" fill="#E5E7EB"/>
                                <rect x="110" y="40" width="10" height="50" rx="5" fill="#4F46E5"/>

                                <rect x="130" y="30" width="10" height="60" rx="5" fill="#E5E7EB"/>
                                <rect x="130" y="35" width="10" height="55" rx="5" fill="#4F46E5"/>
                            </svg>
                        </div>
                        <h3>Happy Customers</h3>
                        <div class="statistic-value">5,000+</div>
                    </div>

                    <div class="statistic-card">
                        <div class="statistic-svg">
                            <svg width="80" height="80" viewBox="0 0 200 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <!-- Background Circle -->
                                <circle cx="100" cy="60" r="50" fill="#E0E7FF" opacity="0.5"/>

                                <!-- Circular Progress -->
                                <circle cx="100" cy="60" r="40" stroke="#E5E7EB" stroke-width="8"/>
                                <path d="M100 20 A40 40 0 1 1 60 60" stroke="#4F46E5" stroke-width="8" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <h3>Loans Funded</h3>
                        <div class="statistic-value">$10M+</div>
                    </div>

                    <div class="statistic-card">
                        <div class="statistic-svg">
                            <svg width="80" height="80" viewBox="0 0 200 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <!-- Background Circle -->
                                <circle cx="100" cy="60" r="50" fill="#E0E7FF" opacity="0.5"/>

                                <!-- Star Rating -->
                                <path d="M70 70 L80 50 L90 70 L70 60 L90 60 Z" fill="#4F46E5"/>
                                <path d="M100 70 L110 50 L120 70 L100 60 L120 60 Z" fill="#4F46E5"/>
                                <path d="M130 70 L140 50 L150 70 L130 60 L150 60 Z" fill="#4F46E5"/>
                                <path d="M160 70 L170 50 L180 70 L160 60 L180 60 Z" fill="#4F46E5"/>
                                <path d="M190 70 L200 50 L210 70 L190 60 L205 60 Z" fill="#4F46E5" fill-opacity="0.5"/>
                            </svg>
                        </div>
                        <h3>Customer Rating</h3>
                        <div class="statistic-value">4.9/5</div>
                    </div>

                    <div class="statistic-card">
                        <div class="statistic-svg">
                            <svg width="80" height="80" viewBox="0 0 200 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <!-- Background Circle -->
                                <circle cx="100" cy="60" r="50" fill="#E0E7FF" opacity="0.5"/>

                                <!-- Clock Icon -->
                                <circle cx="100" cy="60" r="35" stroke="#4F46E5" stroke-width="4" fill="none"/>
                                <line x1="100" y1="60" x2="100" y2="40" stroke="#4F46E5" stroke-width="4" stroke-linecap="round"/>
                                <line x1="100" y1="60" x2="115" y2="70" stroke="#4F46E5" stroke-width="4" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <h3>Fast Approval</h3>
                        <div class="statistic-value">24 hrs</div>
                    </div>
                </div>

                <div class="statistics-cta">
                    <a href="<?php echo BASE_URL; ?>/?page=login" class="button button-primary">Try Our Loan Calculator</a>
                    <a href="<?php echo BASE_URL; ?>/?page=login" class="button button-secondary">Apply Now</a>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="how-it-works-section section-primary-light">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">Simple Process</span>
                <h2>How It Works</h2>
                <p>Get your loan in three easy steps</p>
            </div>

            <div class="steps-container">
                <div class="step-card">
                    <div class="step-number-container">
                        <div class="step-number">1</div>
                    </div>
                    <div class="step-icon">
                        <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="30" cy="30" r="30" fill="#E0E7FF"/>
                            <rect x="15" y="20" width="30" height="20" rx="3" fill="white" stroke="#4F46E5" stroke-width="2"/>
                            <rect x="20" y="25" width="20" height="2.5" rx="1" fill="#4F46E5"/>
                            <rect x="20" y="30" width="20" height="2.5" rx="1" fill="#4F46E5"/>
                            <rect x="20" y="35" width="10" height="2.5" rx="1" fill="#4F46E5"/>
                        </svg>
                    </div>
                    <div class="step-content">
                        <h3>Apply Online</h3>
                        <p>Fill out our simple online application form in minutes. No paperwork, no hassle.</p>
                        <ul class="step-features">
                            <li><i class="fas fa-check"></i> Quick 5-minute application</li>
                            <li><i class="fas fa-check"></i> Secure document upload</li>
                            <li><i class="fas fa-check"></i> Mobile-friendly process</li>
                        </ul>
                    </div>
                </div>

                <div class="step-card">
                    <div class="step-number-container">
                        <div class="step-number">2</div>
                    </div>
                    <div class="step-icon">
                        <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="30" cy="30" r="30" fill="#E0E7FF"/>
                            <rect x="15" y="20" width="30" height="20" rx="3" fill="white" stroke="#4F46E5" stroke-width="2"/>
                            <path d="M20 30 L25 35 L40 20" stroke="#4F46E5" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="step-content">
                        <h3>Get Approved</h3>
                        <p>Receive a quick decision on your application. Most loans are approved within 24 hours.</p>
                        <ul class="step-features">
                            <li><i class="fas fa-circle"></i> Fast credit assessment</li>
                            <li><i class="fas fa-circle"></i> Transparent approval criteria</li>
                            <li><i class="fas fa-circle"></i> Real-time status updates</li>
                        </ul>
                    </div>
                </div>

                <div class="step-card">
                    <div class="step-number-container">
                        <div class="step-number">3</div>
                    </div>
                    <div class="step-icon">
                        <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="30" cy="30" r="30" fill="#E0E7FF"/>
                            <rect x="15" y="20" width="30" height="20" rx="3" fill="white" stroke="#4F46E5" stroke-width="2"/>
                            <circle cx="30" cy="30" r="7.5" fill="#4F46E5" opacity="0.2"/>
                            <text x="30" y="33" text-anchor="middle" font-size="10" fill="#4F46E5" font-weight="bold">$</text>
                        </svg>
                    </div>
                    <div class="step-content">
                        <h3>Receive Funds</h3>
                        <p>Once approved, funds are transferred directly to your bank account within 1-2 business days.</p>
                        <ul class="step-features">
                            <li><i class="fas fa-circle"></i> Direct bank deposit</li>
                            <li><i class="fas fa-circle"></i> Secure fund transfers</li>
                            <li><i class="fas fa-circle"></i> Flexible repayment options</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="section-cta">
                <a href="<?php echo BASE_URL; ?>/?page=login" class="button button-primary">Start Your Application</a>
                <a href="<?php echo BASE_URL; ?>/?page=contact" class="button button-secondary">Have Questions?</a>
            </div>
        </div>
    </section>

    <?php
    // Include guest loan application form
    include INCLUDES_PATH . '/components/guest_loan_form.php';
    include INCLUDES_PATH . '/components/guest_loan_form_html.php';
    ?>

    <section class="features-section section-light" style="background-image: url('<?php echo BASE_URL; ?>/assets/images/features-bg.webp'); background-size: cover; background-position: center; background-attachment: fixed;">
        <div class="section-container" style="background: rgba(255,255,255,0.95); border-radius: 20px; padding: 60px 40px;">
            <div class="section-header">
                <span class="section-badge">Why Choose Us</span>
                <h2>Features That Set Us Apart</h2>
                <p>Experience the benefits of our modern loan platform</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <div class="progress-circle">
                            <i class="fas fa-bolt"></i>
                        </div>
                    </div>
                    <h3>Fast Approval</h3>
                    <div class="feature-content">
                        <p>Get a decision on your loan application within hours, not days.</p>
                        <ul class="feature-details">
                            <li><i class="fas fa-circle"></i> Applications processed in real-time</li>
                            <li><i class="fas fa-circle"></i> Automated credit assessment</li>
                            <li><i class="fas fa-circle"></i> Instant approval notifications</li>
                        </ul>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <div class="progress-circle">
                            <i class="fas fa-lock"></i>
                        </div>
                    </div>
                    <h3>Secure Process</h3>
                    <div class="feature-content">
                        <p>Your data is protected with bank-level security and encryption.</p>
                        <ul class="feature-details">
                            <li><i class="fas fa-circle"></i> 256-bit SSL encryption</li>
                            <li><i class="fas fa-circle"></i> Secure document storage</li>
                            <li><i class="fas fa-circle"></i> Regular security audits</li>
                        </ul>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <div class="progress-circle">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                    </div>
                    <h3>Competitive Rates</h3>
                    <div class="feature-content">
                        <p>Enjoy some of the most competitive interest rates in the industry.</p>
                        <ul class="feature-details">
                            <li><i class="fas fa-circle"></i> Rates starting from 5.9% APR</li>
                            <li><i class="fas fa-circle"></i> Personalized rate quotes</li>
                            <li><i class="fas fa-circle"></i> Rate match guarantee</li>
                        </ul>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <div class="progress-circle">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                    </div>
                    <h3>Transparent Process</h3>
                    <div class="feature-content">
                        <p>Our loan process is fully transparent with clear terms and conditions.</p>
                        <ul class="feature-details">
                            <li><i class="fas fa-circle"></i> Detailed loan agreements</li>
                            <li><i class="fas fa-circle"></i> Clear repayment schedules</li>
                            <li><i class="fas fa-circle"></i> No surprise charges</li>
                        </ul>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <div class="progress-circle">
                            <i class="fas fa-headset"></i>
                        </div>
                    </div>
                    <h3>Dedicated Support</h3>
                    <div class="feature-content">
                        <p>Our customer service team is available to assist you every step of the way.</p>
                        <ul class="feature-details">
                            <li><i class="fas fa-circle"></i> Live chat support</li>
                            <li><i class="fas fa-circle"></i> Dedicated loan advisors</li>
                            <li><i class="fas fa-circle"></i> Extended support hours</li>
                        </ul>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <div class="progress-circle">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                    </div>
                    <h3>Mobile Friendly</h3>
                    <div class="feature-content">
                        <p>Apply for a loan and manage your account from any device, anywhere.</p>
                        <ul class="feature-details">
                            <li><i class="fas fa-circle"></i> Responsive design</li>
                            <li><i class="fas fa-circle"></i> Document upload from mobile</li>
                            <li><i class="fas fa-circle"></i> Mobile payment options</li>
                        </ul>
                    </div>
                </div>


            </div>
        </div>
    </section>





    <!-- Dashboard Preview Section -->
    <section class="dashboard-preview-section section-darker">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">USER EXPERIENCE</span>
                <h2>Powerful Dashboard Interface</h2>
                <p>Manage your loans with our intuitive and feature-rich dashboard</p>
            </div>

            <div class="dashboard-preview">
                <div class="dashboard-decorations left">
                    <div class="decoration-icon">
                        <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="30" cy="30" r="30" fill="#E0E7FF"/>
                            <path d="M20 30L25 35L40 20" stroke="#4F46E5" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="decoration-icon">
                        <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="30" cy="30" r="30" fill="#E0E7FF"/>
                            <rect x="15" y="20" width="30" height="20" rx="3" fill="white" stroke="#4F46E5" stroke-width="2"/>
                            <rect x="20" y="25" width="20" height="2.5" rx="1" fill="#4F46E5"/>
                            <rect x="20" y="30" width="20" height="2.5" rx="1" fill="#4F46E5"/>
                            <rect x="20" y="35" width="10" height="2.5" rx="1" fill="#4F46E5"/>
                        </svg>
                    </div>
                    <div class="decoration-icon">
                        <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="30" cy="30" r="30" fill="#E0E7FF"/>
                            <path d="M30 15V45M15 30H45" stroke="#4F46E5" stroke-width="3" stroke-linecap="round"/>
                        </svg>
                    </div>
                </div>

                <div class="dashboard-image full-width">
                    <img src="<?php echo ASSETS_URL; ?>/images/dashboard-preview.svg" alt="Dashboard Preview">
                </div>

                <div class="dashboard-decorations right">
                    <div class="decoration-icon">
                        <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="30" cy="30" r="30" fill="#E0E7FF"/>
                            <path d="M20 20H40V40H20V20Z" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M20 27H40" stroke="#4F46E5" stroke-width="2" stroke-linecap="round"/>
                            <path d="M27 27V40" stroke="#4F46E5" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <div class="decoration-icon">
                        <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="30" cy="30" r="30" fill="#E0E7FF"/>
                            <path d="M20 35C20 28.3726 25.3726 23 32 23C38.6274 23 44 28.3726 44 35" stroke="#4F46E5" stroke-width="2" stroke-linecap="round"/>
                            <path d="M20 35V40H44V35" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <circle cx="32" cy="30" r="3" fill="#4F46E5"/>
                        </svg>
                    </div>
                    <div class="decoration-icon">
                        <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="30" cy="30" r="30" fill="#E0E7FF"/>
                            <path d="M20 20L40 40M40 20L20 40" stroke="#4F46E5" stroke-width="3" stroke-linecap="round"/>
                        </svg>
                    </div>
                </div>
            </div>

            <div class="cta-button-container">
                <a href="<?php echo BASE_URL; ?>/?page=login" class="button button-primary button-large">Explore Dashboard</a>
            </div>
        </div>
    </section>

    <section class="loan-products-section section-light">
        <div class="section-container">
            <div class="section-header">
            <span class="section-badge">Flexible Options</span>
            <h2>Our Loan Products</h2>
            <p>Explore our range of loan products designed to meet your financial needs</p>
        </div>

        <div class="loan-products-grid">
            <?php
            // Get database connection
            $db = getDbConnection();

            // Get loan products
            $result = $db->query("SELECT * FROM loan_products ORDER BY id ASC LIMIT 3");

            if ($result && $result->num_rows > 0) {
                while ($product = $result->fetch_assoc()) {
                    // Get product icon based on name
                    $icon = 'fa-money-bill-wave';
                    if (stripos($product['name'], 'personal') !== false) {
                        $icon = 'fa-user';
                    } elseif (stripos($product['name'], 'business') !== false) {
                        $icon = 'fa-briefcase';
                    } elseif (stripos($product['name'], 'education') !== false) {
                        $icon = 'fa-graduation-cap';
                    } elseif (stripos($product['name'], 'home') !== false || stripos($product['name'], 'mortgage') !== false) {
                        $icon = 'fa-home';
                    } elseif (stripos($product['name'], 'auto') !== false || stripos($product['name'], 'car') !== false) {
                        $icon = 'fa-car';
                    }

                    // Get color class based on product ID for variety
                    $colorClasses = ['blue', 'purple', 'green', 'orange'];
                    $colorClass = $colorClasses[($product['id'] - 1) % count($colorClasses)];
                    ?>
                    <div class="loan-product-card product-<?php echo $colorClass; ?>">
                        <div class="product-icon">
                            <i class="fas <?php echo $icon; ?>"></i>
                        </div>
                        <div class="product-badge">Popular Choice</div>
                        <h3><?php echo htmlspecialchars($product['name']); ?></h3>
                        <p class="product-description">
                            <?php
                            // Generate a description if none exists
                            echo isset($product['description']) && !empty($product['description'])
                                ? htmlspecialchars($product['description'])
                                : 'Flexible ' . htmlspecialchars($product['name']) . ' with competitive rates and easy repayment options.';
                            ?>
                        </p>
                        <div class="loan-product-details">
                            <div class="loan-product-detail">
                                <span class="detail-label">Amount Range</span>
                                <span class="detail-value">$<?php echo number_format($product['min_amount']); ?> - $<?php echo number_format($product['max_amount']); ?></span>
                            </div>
                            <div class="loan-product-detail">
                                <span class="detail-label">Interest Rate</span>
                                <span class="detail-value"><?php echo $product['interest_rate']; ?>% APR</span>
                            </div>
                            <div class="loan-product-detail">
                                <span class="detail-label">Term</span>
                                <span class="detail-value"><?php echo $product['term_months']; ?> months</span>
                            </div>
                            <?php if (isset($product['processing_fee']) && $product['processing_fee'] > 0): ?>
                            <div class="loan-product-detail">
                                <span class="detail-label">Processing Fee</span>
                                <span class="detail-value"><?php echo $product['processing_fee']; ?>%</span>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="product-actions">
                            <a href="<?php echo BASE_URL; ?>/?page=login" class="button button-secondary">Calculate</a>
                            <a href="<?php echo BASE_URL; ?>/?page=login" class="button button-primary">Apply Now</a>
                        </div>
                    </div>
                    <?php
                }
            } else {
                ?>
                <div class="empty-state">
                    <i class="fas fa-info-circle"></i>
                    <p>No loan products available at the moment. Please check back later.</p>
                </div>
                <?php
            }
            ?>
        </div>

        <div class="view-all-container">
            <a href="<?php echo BASE_URL; ?>/?page=login" class="view-all-link">
                View All Loan Products <i class="fas fa-arrow-right"></i>
            </a>
        </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section section-darker">
        <div class="section-container">
            <div class="section-header">
            <span class="section-badge">Got Questions?</span>
            <h2>Frequently Asked Questions</h2>
            <p>Find answers to common questions about our loan services</p>
        </div>

        <div class="faq-container">
            <div class="faq-column">
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How do I apply for a loan?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>Applying for a loan is simple. Create an account, complete our online application form, upload the required documents, and submit your application. Our team will review it and get back to you within 24 hours.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What documents do I need to apply?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>Typically, you'll need to provide proof of identity (government-issued ID), proof of income (pay stubs or tax returns), and bank statements. Specific requirements may vary based on the loan type and amount.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How long does the approval process take?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>Most loan applications are processed within 24 hours. Once approved, funds are typically disbursed within 1-2 business days, depending on your bank.</p>
                    </div>
                </div>
            </div>

            <div class="faq-column">
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What are the interest rates?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>Our interest rates vary based on the loan type, amount, and your credit profile. Rates typically range from 5.99% to 15.99% APR. You can see specific rates for each loan product on our platform.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Can I pay off my loan early?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>Yes, you can pay off your loan early without any prepayment penalties. Early repayment can help you save on interest costs.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What if I miss a payment?</h3>
                        <span class="faq-toggle"><i class="fas fa-plus"></i></span>
                    </div>
                    <div class="faq-answer">
                        <p>If you anticipate difficulty making a payment, please contact us immediately. Late payments may incur fees and could affect your credit score. We offer flexible repayment options for customers facing temporary financial hardship.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="faq-more">
            <p>Have more questions? We're here to help!</p>
            <a href="<?php echo BASE_URL; ?>/?page=contact" class="button button-secondary">Contact Us</a>
        </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials-section section-darker">
        <div class="section-container">
            <div class="section-header">
            <span class="section-badge">Customer Stories</span>
            <h2>What Our Clients Say</h2>
            <p>Don't just take our word for it - hear from our satisfied customers</p>
        </div>

        <div class="testimonials-slider">
            <div class="testimonial-card">
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
                <p class="testimonial-text">"Pharaoh Finance Private and Fast Loans made getting a business loan incredibly easy. Their online application took me less than 10 minutes to complete, and I had approval within 24 hours. The funds were in my account the next day!"</p>
                <div class="testimonial-author">
                    <div class="author-avatar">
                        <img src="<?php echo ASSETS_URL; ?>/images/avatar-1.jpg" alt="John D." onerror="this.src='https://ui-avatars.com/api/?name=John+D&background=4f46e5&color=fff'">
                    </div>
                    <div class="author-info">
                        <h4>John D.</h4>
                        <p>Small Business Owner</p>
                    </div>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                </div>
                <p class="testimonial-text">"I needed a personal loan for home renovations and was dreading the paperwork. Pharaoh Finance Private and Fast Loans' process was completely digital and straightforward. Their customer service team was also incredibly helpful when I had questions."</p>
                <div class="testimonial-author">
                    <div class="author-avatar">
                        <img src="<?php echo ASSETS_URL; ?>/images/avatar-2.jpg" alt="Sarah M." onerror="this.src='https://ui-avatars.com/api/?name=Sarah+M&background=4f46e5&color=fff'">
                    </div>
                    <div class="author-info">
                        <h4>Sarah M.</h4>
                        <p>Homeowner</p>
                    </div>
                </div>
            </div>

            <div class="testimonial-card">
                <div class="testimonial-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star-half-alt"></i>
                </div>
                <p class="testimonial-text">"As a first-time borrower, I was nervous about applying for a loan. The team at Pharaoh Finance Private and Fast Loans guided me through every step of the process. Their dashboard makes it easy to track my payments and see my progress."</p>
                <div class="testimonial-author">
                    <div class="author-avatar">
                        <img src="<?php echo ASSETS_URL; ?>/images/avatar-3.jpg" alt="Michael T." onerror="this.src='https://ui-avatars.com/api/?name=Michael+T&background=4f46e5&color=fff'">
                    </div>
                    <div class="author-info">
                        <h4>Michael T.</h4>
                        <p>Student</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="testimonial-indicators">
            <span class="indicator active"></span>
            <span class="indicator"></span>
            <span class="indicator"></span>
        </div>
        </div>
    </section>

    <section class="cta-section section-gradient">
        <div class="section-container">
            <div class="cta-content">
            <span class="cta-badge">Get Started Today</span>
            <h2>Ready to Take the Next Step?</h2>
            <p>Create an account and apply for a loan in minutes. No paperwork, no hassle.</p>
            <div class="cta-buttons">
                <a href="<?php echo BASE_URL; ?>/?page=login" class="button button-primary">
                    <i class="fas fa-user-plus"></i> Create Account
                </a>
                <a href="<?php echo BASE_URL; ?>/?page=login" class="button button-secondary">
                    <i class="fas fa-calculator"></i> Calculate Your Loan
                </a>
            </div>
        </div>
        <div class="cta-shape"></div>
        <div class="section-container">
        </div>
    </section>
</div>

<style>
    /* General Styles */
    .home-wrapper {
        width: 100%;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
    }

    /* Desktop/Mobile Visibility Classes */
    .desktop-only {
        display: block !important;
    }

    .mobile-only {
        display: none !important;
    }

    @media (max-width: 768px) {
        .desktop-only {
            display: none !important;
        }

        .mobile-only {
            display: block !important;
        }

        /* Hide the original hero section completely */
        .hero-section.desktop-only {
            display: none !important;
        }
    }

    .section-container {
        width: 100%;
        padding: 0 4rem;
        box-sizing: border-box;
        position: relative;
        z-index: 1;
    }

    @media (max-width: 1200px) {
        .section-container {
            padding: 0 3rem;
        }
    }

    @media (max-width: 992px) {
        .section-container {
            padding: 0 2rem;
        }
    }

    @media (max-width: 768px) {
        .section-container {
            padding: 0 1.5rem;
        }

        /* Mobile app-like hero section */
        .hero-section {
            padding-top: 1rem;
            padding-bottom: 2rem;
            text-align: center;
        }

        .hero-section .section-container {
            flex-direction: column-reverse;
        }

        .hero-content {
            width: 100%;
            padding: 0;
            align-items: center;
        }

        .hero-image {
            width: 100%;
            margin-bottom: 1.5rem;
            position: relative;
            display: flex;
            justify-content: center;
        }

        .hero-image img {
            max-width: 80%;
            margin: 0 auto;
            opacity: 0.8;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 100%;
            background: linear-gradient(180deg, rgba(79, 70, 229, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
            z-index: -1;
            border-bottom-left-radius: 30px;
            border-bottom-right-radius: 30px;
        }

        .hero-badge {
            margin: 0 auto 1rem;
        }

        .hero-content h1 {
            font-size: 2.25rem;
            text-align: center;
            margin-bottom: 1rem;
        }

        .hero-description {
            text-align: center;
            font-size: 1rem;
            margin-bottom: 1.5rem;
        }

        .hero-buttons {
            flex-direction: column;
            width: 100%;
            gap: 0.75rem;
            padding: 0 0.5rem;
        }

        .hero-buttons .button {
            width: 100%;
            justify-content: center;
            border-radius: 50px;
            padding: 1rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .hero-buttons .button-primary {
            background: linear-gradient(90deg, var(--primary-color) 0%, #3b82f6 100%);
            border: none;
        }

        .hero-buttons .button:active {
            transform: translateY(2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .hero-stats {
            justify-content: space-around;
            width: 100%;
            margin-top: 1.5rem;
            background-color: rgba(79, 70, 229, 0.05);
            border-radius: 16px;
            padding: 1rem 0.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .hero-stat {
            position: relative;
            padding: 0.5rem;
            text-align: center;
        }

        .stat-number {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--text-muted);
            white-space: nowrap;
        }
    }

    @media (max-width: 480px) {
        .section-container {
            padding: 0 1rem;
        }
    }

    /* Section Backgrounds */
    .section-light {
        background-color: #ffffff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .section-dark {
        background-color: #f8f9fc; /* Slightly blue-tinted white */
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .section-darker {
        background-color: #f5f7fa; /* Darker shade of light white */
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .section-primary-light {
        background-color: rgba(79, 70, 229, 0.05); /* Very light primary color */
        border-bottom: 1px solid rgba(79, 70, 229, 0.1);
    }

    .section-gradient {
        background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
        border: none;
    }

    /* Section Badge */
    .section-badge, .hero-badge, .cta-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        background-color: rgba(79, 70, 229, 0.1);
        color: var(--primary-color);
        border-radius: 2rem;
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    /* Section Headers */
    .section-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .section-header h2 {
        font-size: 2.25rem;
        color: var(--text-color);
        margin-bottom: 0.75rem;
        position: relative;
    }

    .section-header p {
        font-size: 1.125rem;
        color: var(--text-muted);
        margin: 0 auto;
        max-width: 800px;
    }

    /* Common Section Styles */
    section {
        padding: 5rem 0;
        position: relative;
        width: 100%;
        box-sizing: border-box;
    }

    /* Add spacing between sections on mobile */
    @media (max-width: 768px) {
        section {
            padding: 4rem 0;
        }
    }

    /* Hero Section */
    .hero-section {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        overflow: hidden;
    }

    .hero-section .section-container {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 2rem;
        width: 100%;
    }

    .hero-content {
        flex: 1;
        width: 50%;
        padding-right: 2rem;
        padding-left: 0;
    }

    .hero-image {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }

    .hero-image img {
        max-width: 100%;
        height: auto;
        position: relative;
        z-index: 2;
    }

    .hero-shape-1, .hero-shape-2 {
        position: absolute;
        z-index: 1;
    }

    .hero-shape-1 {
        width: 300px;
        height: 300px;
        top: -100px;
        right: -50px;
    }

    .hero-shape-2 {
        width: 200px;
        height: 200px;
        bottom: -50px;
        right: 100px;
    }

    .hero-shape-1 img, .hero-shape-2 img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .loan-icon {
        position: absolute;
        z-index: 2;
        animation: float 3s ease-in-out infinite;
    }

    .loan-icon-1 {
        top: 15%;
        left: 10%;
        animation-delay: 0s;
    }

    .loan-icon-2 {
        top: 25%;
        right: 15%;
        animation-delay: 0.5s;
    }

    .loan-icon-3 {
        bottom: 20%;
        left: 20%;
        animation-delay: 1s;
    }

    .loan-icon-4 {
        bottom: 30%;
        right: 10%;
        animation-delay: 1.5s;
    }

    @keyframes float {
        0% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
        100% {
            transform: translateY(0px);
        }
    }

    .hero-content h1 {
        font-size: 3rem;
        color: var(--text-color);
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .text-gradient {
        background: linear-gradient(90deg, var(--primary-color), #3b82f6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        color: transparent;
    }

    .hero-description {
        font-size: 1.25rem;
        color: var(--text-muted);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .hero-buttons {
        display: flex;
        gap: 1rem;
        margin-bottom: 2.5rem;
    }

    .hero-buttons .button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .hero-stats {
        display: flex;
        gap: 2rem;
    }

    .hero-stat {
        display: flex;
        flex-direction: column;
    }

    .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--text-muted);
    }

    /* How It Works Section */
    .how-it-works-section {
        padding: 5rem 0;
        width: 100%;
    }

    .how-it-works-section .container {
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .steps-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        margin-bottom: 3rem;
        width: 100%;
    }

    .step-card {
        background-color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .step-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number-container {
        margin-bottom: 1rem;
    }

    .step-number {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        font-weight: 700;
        margin: 0 auto;
    }

    .step-icon {
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .step-content {
        width: 100%;
    }

    .step-content h3 {
        font-size: 1.25rem;
        color: var(--text-color);
        margin-bottom: 0.75rem;
    }

    .step-content p {
        color: var(--text-muted);
        font-size: 0.875rem;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .step-features {
        list-style: none;
        padding: 0;
        margin: 0;
        text-align: left;
    }

    .step-features li {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        color: var(--text-muted);
        font-size: 0.875rem;
    }

    .step-features li i {
        color: var(--primary-color);
        font-size: 0.5rem;
    }

    .section-cta {
        text-align: center;
        display: flex;
        justify-content: center;
        gap: 1rem;
    }

    /* Responsive styles for How It Works */
    @media (max-width: 992px) {
        .steps-container {
            grid-template-columns: 1fr;
        }
    }

    /* Features Section */
    .features-section {
        padding: 5rem 0;
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        width: 100%;
    }

    @media (max-width: 992px) {
        .features-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 576px) {
        .features-grid {
            grid-template-columns: 1fr;
        }
    }

    .feature-card {
        background-color: white;
        border-radius: 0.75rem;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .feature-icon {
        margin-bottom: 1rem;
    }

    .progress-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: var(--primary-color);
        display: flex;
        justify-content: center;
        align-items: center;
        color: white;
        font-size: 1.5rem;
    }

    .feature-content {
        width: 100%;
        text-align: left;
    }

    .feature-content h3 {
        font-size: 1.25rem;
        color: var(--text-color);
        margin-bottom: 0.75rem;
    }

    .feature-content p {
        color: var(--text-muted);
        font-size: 0.875rem;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .feature-details {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .feature-details li {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        color: var(--text-muted);
        font-size: 0.875rem;
    }

    .feature-details li i {
        color: var(--primary-color);
        font-size: 0.5rem;
    }

    @media (max-width: 768px) {
        .feature-card {
            flex-direction: column;
            text-align: center;
            align-items: center;
        }

        .feature-details li {
            justify-content: center;
        }
    }

    /* Loan Calculator Section */
    .loan-calculator-section {
        padding: 5rem 0;
    }

    .loan-calculator-section .container {
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }

    .calculator-container {
        background-color: white;
        border-radius: 1rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        margin-top: 3rem;
    }

    .calculator-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        padding: 2rem;
    }

    .calculator-illustration {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--primary-light, #E0E7FF);
        border-radius: 0.75rem;
        padding: 1rem;
    }

    .calculator-form {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .form-group label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-color);
    }

    .form-group input[type="number"] {
        padding: 0.75rem;
        border: 1px solid var(--border-color, #E5E7EB);
        border-radius: 0.5rem;
        font-size: 1rem;
        width: 100%;
        padding-left: 1.5rem;
    }

    .form-group {
        position: relative;
    }

    .input-currency-symbol,
    .input-percentage-symbol {
        position: absolute;
        left: 0.75rem;
        top: 2.5rem;
        font-size: 0.875rem;
        color: var(--text-muted, #6B7280);
    }

    .input-percentage-symbol {
        right: 0.75rem;
        left: auto;
    }

    .range-container {
        position: relative;
        padding: 0.5rem 0;
        margin-top: 0.5rem;
    }

    .loan-products-dropdown {
        margin-top: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .loan-products-dropdown label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 0.5rem;
    }

    .loan-products-dropdown select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color, #E5E7EB);
        border-radius: 0.5rem;
        font-size: 1rem;
        background-color: white;
    }

    input[type="range"] {
        width: 100%;
        -webkit-appearance: none;
        height: 6px;
        background: var(--border-color, #E5E7EB);
        border-radius: 3px;
        outline: none;
    }

    input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: var(--primary-color);
        cursor: pointer;
    }

    .range-values {
        display: flex;
        justify-content: space-between;
        font-size: 0.75rem;
        color: var(--text-muted, #6B7280);
        margin-top: 0.25rem;
    }

    .calculator-results {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        padding: 2rem;
        background-color: var(--background-color, #F9FAFB);
        border-top: 1px solid var(--border-color, #E5E7EB);
    }

    .result-card {
        background-color: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .result-card h3 {
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 0.75rem;
    }

    .result-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .calculator-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
        padding: 2rem;
        border-top: 1px solid var(--border-color, #E5E7EB);
    }

    @media (max-width: 992px) {
        .calculator-grid {
            grid-template-columns: 1fr;
        }

        .calculator-results {
            grid-template-columns: 1fr;
        }
    }

    /* Dashboard Preview Section */
    .dashboard-preview-section {
        padding: 5rem 0;
    }

    .dashboard-preview-section .container {
        width: 100%;
        max-width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .dashboard-preview {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 3rem 0;
        width: 100%;
        position: relative;
    }

    .dashboard-decorations {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        width: 80px;
    }

    .dashboard-decorations.left {
        margin-right: 2rem;
    }

    .dashboard-decorations.right {
        margin-left: 2rem;
    }

    .decoration-icon {
        animation: float 3s ease-in-out infinite;
    }

    .decoration-icon:nth-child(2) {
        animation-delay: 0.5s;
    }

    .decoration-icon:nth-child(3) {
        animation-delay: 1s;
    }

    @keyframes float {
        0% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-10px);
        }
        100% {
            transform: translateY(0px);
        }
    }

    .dashboard-image.full-width {
        width: 100%;
        max-width: 1400px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .dashboard-image img {
        width: 100%;
        height: auto;
        max-height: 350px;
        border-radius: 0;
        object-fit: cover;
        object-position: top center;
    }

    .cta-button-container {
        display: flex;
        justify-content: center;
        margin-top: 3rem;
    }

    .cta-button-container .button {
        padding: 0.75rem 2rem;
        font-size: 1.1rem;
    }

    .button-large {
        padding: 1rem 3rem !important;
        font-size: 1.25rem !important;
        font-weight: 600;
    }

    /* Dashboard responsive styles */
    @media (max-width: 992px) {
        .dashboard-image.full-width {
            max-width: 100%;
        }

        .dashboard-decorations {
            width: 60px;
        }
    }

    @media (max-width: 768px) {
        .dashboard-decorations.left {
            display: none;
        }

        .dashboard-decorations.right {
            display: none;
        }
    }

    /* Testimonials Section */
    .testimonials-section {
        /* Section-specific styles */
    }

    .testimonials-slider {
        display: flex;
        gap: 2rem;
        width: 100%;
        margin-bottom: 2rem;
        overflow-x: auto;
        padding: 1rem 0.5rem;
        scrollbar-width: none; /* Firefox */
    }

    .testimonials-slider::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Edge */
    }

    .testimonial-card {
        flex: 0 0 calc(33.333% - 1.5rem);
        min-width: 300px;
        background-color: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .testimonial-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .testimonial-rating {
        color: #f59e0b;
        margin-bottom: 1rem;
    }

    .testimonial-text {
        color: var(--text-color);
        font-size: 0.875rem;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        font-style: italic;
    }

    .testimonial-author {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .author-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
    }

    .author-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .author-info h4 {
        font-size: 1rem;
        color: var(--text-color);
        margin: 0 0 0.25rem;
    }

    .author-info p {
        font-size: 0.75rem;
        color: var(--text-muted);
        margin: 0;
    }

    .testimonial-indicators {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
    }

    .indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #e5e7eb;
        cursor: pointer;
    }

    .indicator.active {
        background-color: var(--primary-color);
    }

    /* Loan Products Section */
    .loan-products-section {
        padding: 5rem 0;
    }

    .loan-products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        width: 100%;
    }

    .loan-product-card {
        background-color: var(--card-background);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        position: relative;
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .loan-product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .product-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .product-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background-color: rgba(16, 185, 129, 0.1);
        color: #10b981;
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
    }

    .product-blue .product-icon {
        background-color: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
    }

    .product-purple .product-icon {
        background-color: rgba(79, 70, 229, 0.1);
        color: #4f46e5;
    }

    .product-green .product-icon {
        background-color: rgba(16, 185, 129, 0.1);
        color: #10b981;
    }

    .product-orange .product-icon {
        background-color: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
    }

    .loan-product-card h3 {
        font-size: 1.5rem;
        color: var(--text-color);
        margin-bottom: 0.75rem;
    }

    .product-description {
        color: var(--text-muted);
        font-size: 0.875rem;
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }

    .loan-product-details {
        margin-bottom: 2rem;
    }

    .loan-product-detail {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--border-color);
    }

    .detail-label {
        color: var(--text-muted);
        font-size: 0.875rem;
    }

    .detail-value {
        font-weight: 600;
        color: var(--text-color);
        font-size: 0.875rem;
    }

    .product-actions {
        display: flex;
        gap: 1rem;
    }

    .product-actions .button {
        flex: 1;
        text-align: center;
    }

    .view-all-container {
        text-align: center;
        margin-top: 3rem;
    }

    .view-all-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border: 2px solid var(--primary-color);
        border-radius: 0.5rem;
        transition: all 0.2s ease;
    }

    .view-all-link:hover {
        background-color: var(--primary-color);
        color: white;
    }

    /* FAQ Section */
    .faq-section {
        /* Section-specific styles */
    }

    .faq-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
        width: 100%;
        margin-bottom: 3rem;
    }

    .faq-item {
        background-color: white;
        border-radius: 0.75rem;
        overflow: hidden;
        margin-bottom: 1rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .faq-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    }

    .faq-question {
        padding: 1.25rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .faq-question:hover {
        background-color: rgba(79, 70, 229, 0.05);
    }

    .faq-question h3 {
        font-size: 1rem;
        color: var(--text-color);
        margin: 0;
    }

    .faq-toggle {
        color: var(--primary-color);
        font-size: 0.875rem;
        transition: transform 0.2s ease;
    }

    .faq-item.active .faq-toggle {
        transform: rotate(45deg);
    }

    .faq-answer {
        padding: 0 1.25rem;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease, padding 0.3s ease;
    }

    .faq-item.active .faq-answer {
        padding: 0 1.25rem 1.25rem;
        max-height: 500px;
    }

    .faq-answer p {
        color: var(--text-muted);
        font-size: 0.875rem;
        line-height: 1.6;
        margin: 0;
    }

    .faq-more {
        text-align: center;
    }

    .faq-more p {
        color: var(--text-muted);
        margin-bottom: 1rem;
    }

    /* CTA Section */
    .cta-section {
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .cta-shape {
        position: absolute;
        width: 300px;
        height: 300px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        top: -150px;
        right: -150px;
    }

    .cta-badge {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
    }

    .cta-content h2 {
        font-size: 2.25rem;
        color: white;
        margin-bottom: 1rem;
        position: relative;
    }

    .cta-content p {
        font-size: 1.125rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 2rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .cta-buttons {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }

    .cta-content .button-primary {
        background-color: white;
        color: var(--primary-color);
    }

    .cta-content .button-primary:hover {
        background-color: rgba(255, 255, 255, 0.9);
    }

    .cta-content .button-secondary {
        background-color: transparent;
        color: white;
        border: 2px solid white;
    }

    .cta-content .button-secondary:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .cta-content .button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        grid-column: 1 / -1;
        background-color: #f9fafb;
        border-radius: 0.75rem;
    }

    .empty-state i {
        font-size: 2rem;
        color: var(--text-muted);
        margin-bottom: 1rem;
    }

    .empty-state p {
        color: var(--text-muted);
        font-size: 1rem;
    }

    /* Responsive Styles */
    @media (max-width: 992px) {
        .hero-section {
            flex-direction: column;
            padding: 3rem 0;
        }

        .hero-content {
            width: 100%;
            max-width: 100%;
            padding-left: 0;
            padding-right: 0;
            margin-bottom: 3rem;
            text-align: center;
        }

        .hero-buttons {
            justify-content: center;
        }

        .hero-stats {
            justify-content: center;
        }

        .steps-container {
            flex-direction: column;
            gap: 3rem;
        }

        .faq-container {
            grid-template-columns: 1fr;
            width: 100%;
        }
    }

    @media (max-width: 768px) {
        .hero-content h1 {
            font-size: 2.25rem;
        }

        .hero-description {
            font-size: 1rem;
        }

        .hero-buttons {
            flex-direction: column;
            align-items: center;
        }

        .hero-buttons .button {
            width: 100%;
            max-width: 300px;
        }

        .hero-stats {
            flex-direction: column;
            gap: 1rem;
            align-items: center;
        }

        .section-header h2 {
            font-size: 1.75rem;
        }

        .section-header p {
            font-size: 1rem;
        }

        .feature-card,
        .loan-product-card {
            padding: 1.5rem;
        }

        .testimonial-card {
            flex: 0 0 calc(100% - 2rem);
        }

        .cta-content h2 {
            font-size: 1.75rem;
        }

        .cta-content p {
            font-size: 1rem;
        }

        .cta-buttons {
            flex-direction: column;
            align-items: center;
        }

        .cta-buttons .button {
            width: 100%;
            max-width: 300px;
        }
    }

    @media (max-width: 480px) {
        /* Mobile app-like styles */
        body {
            padding-bottom: 70px; /* Space for bottom navigation */
        }

        .home-wrapper {
            overflow-x: hidden;
        }

        /* Card-like sections with rounded corners */
        .section-light, .section-darker, .section-primary-light {
            border-radius: 20px;
            margin: 0 10px 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        /* Floating action buttons */
        .button-primary {
            border-radius: 50px;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .button-secondary {
            border-radius: 50px;
        }

        /* App-like cards */
        .feature-card, .loan-product-card, .statistic-card, .step-card, .faq-item {
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 16px;
            border: none;
        }

        /* Bottom spacing */
        .faq-item {
            margin-left: 0;
            margin-right: 0;
        }

        .testimonial-card {
            min-width: 260px;
            border-radius: 16px;
        }

        .step-card {
            padding: 1.5rem 1rem;
        }

        /* Larger touch targets */
        .button, .social-link, .feature-card, .loan-product-card {
            min-height: 44px; /* Minimum Apple recommended touch target size */
        }

        /* Smooth scrolling */
        .section-container, .container {
            padding-left: 15px;
            padding-right: 15px;
        }

        /* App-like spacing */
        .section-header {
            margin-bottom: 1.5rem;
        }

        .section-header h2 {
            font-size: 1.75rem;
        }

        /* Larger icons for better visibility */
        .progress-circle {
            width: 70px;
            height: 70px;
            font-size: 1.75rem;
        }

        /* Hero section specific mobile app styling */
        .hero-section {
            border-radius: 0;
            margin: 0;
            box-shadow: none;
            padding-top: 0.5rem;
        }

        .hero-content h1 {
            font-size: 2rem;
            line-height: 1.2;
        }

        .hero-badge {
            font-size: 0.75rem;
            padding: 0.4rem 0.8rem;
        }

        .hero-stats {
            background-color: rgba(79, 70, 229, 0.05);
            border-radius: 16px;
            padding: 1rem;
            margin-top: 1.5rem;
        }

        .stat-number {
            font-size: 1.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
        }

        /* Improve spacing between sections */
        section {
            padding: 2rem 0;
        }

        /* Make CTA more app-like */
        .cta-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
            border-radius: 20px;
            margin: 0 10px 70px; /* Extra bottom margin for mobile nav */
        }

        .cta-buttons {
            flex-direction: column;
            width: 100%;
        }

        .cta-buttons .button {
            width: 100%;
            margin-bottom: 0.5rem;
            justify-content: center;
        }
    }

    /* Statistics Section */
    .statistics-section {
        padding: 5rem 0;
    }

    .statistics-container {
        margin-top: 3rem;
    }

    .statistics-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .statistic-card {
        background-color: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .statistic-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .statistic-svg {
        margin-bottom: 1rem;
    }

    .statistic-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-top: 0.5rem;
    }

    .statistic-card h3 {
        font-size: 1.25rem;
        color: var(--text-color);
        margin-bottom: 0.25rem;
    }

    .statistics-cta {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }

    /* Responsive styles for statistics */
    @media (max-width: 1200px) {
        .statistics-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .statistics-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .statistics-cta {
            flex-direction: column;
            align-items: center;
        }

        .statistics-cta .button {
            width: 100%;
            max-width: 300px;
        }
    }

    /* Mobile Hero Section Styles */
    .mobile-hero-section {
        padding: 0;
        margin-top: -1px; /* Fix any gap */
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
    }

    .mobile-hero-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    /* Creative Hero Section */
    .mobile-hero-creative {
        position: relative;
        padding: 2rem 1rem;
        background: white;
        border-radius: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        overflow: hidden;
    }

    .mobile-hero-creative::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%234f46e5' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
        opacity: 0.5;
        z-index: 0;
    }

    .mobile-hero-icon-wrapper {
        position: relative;
        z-index: 1;
        margin-bottom: 1.5rem;
    }

    .mobile-hero-icon-bg {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 10px 20px rgba(79, 70, 229, 0.2);
    }

    .mobile-hero-icon-bg i {
        font-size: 2.5rem;
        color: white;
    }

    .mobile-hero-text {
        position: relative;
        z-index: 1;
        margin-bottom: 1.5rem;
    }

    .mobile-hero-title {
        font-size: 2.5rem;
        font-weight: 900;
        margin: 0 0 0.5rem;
        line-height: 1.1;
        letter-spacing: -0.5px;
        color: var(--text-color);
    }

    .mobile-hero-subtitle {
        font-size: 1.25rem;
        color: var(--primary-color);
        margin: 0;
        font-weight: 700;
    }

    .mobile-hero-cta-button {
        position: relative;
        z-index: 1;
        background: linear-gradient(90deg, var(--primary-color) 0%, #3b82f6 100%);
        color: white;
        border: none;
        border-radius: 50px;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1.125rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        box-shadow: 0 8px 15px rgba(79, 70, 229, 0.25);
        text-decoration: none;
        justify-content: center;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        width: 80%;
    }

    .mobile-hero-cta-button:active {
        transform: translateY(2px);
        box-shadow: 0 4px 8px rgba(79, 70, 229, 0.2);
    }

    /* Stats Row */
    .mobile-stats-row {
        display: flex;
        justify-content: space-between;
        gap: 0.75rem;
    }

    .mobile-stat-card {
        flex: 1;
        background: white;
        border-radius: 16px;
        padding: 1rem 0.5rem;
        text-align: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .mobile-stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(79, 70, 229, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.5rem;
    }

    .mobile-stat-icon i {
        font-size: 1.25rem;
        color: var(--primary-color);
    }

    .mobile-stat-number {
        font-size: 1.125rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .mobile-stat-label {
        font-size: 0.75rem;
        color: var(--text-muted);
    }

    /* Loan Options */
    .mobile-loan-options {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .mobile-loan-options h3 {
        font-size: 1.25rem;
        margin: 0 0 1rem;
        color: var(--text-color);
    }

    .mobile-loan-cards {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .mobile-loan-card {
        display: flex;
        align-items: center;
        padding: 1rem;
        background: rgba(79, 70, 229, 0.05);
        border-radius: 12px;
        text-decoration: none;
        color: var(--text-color);
    }

    .mobile-loan-card-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .mobile-loan-card-icon i {
        font-size: 1.25rem;
        color: var(--primary-color);
    }

    .mobile-loan-card-content {
        flex: 1;
    }

    .mobile-loan-card-content h4 {
        font-size: 1rem;
        margin: 0 0 0.25rem;
        color: var(--text-color);
    }

    .mobile-loan-card-content p {
        font-size: 0.875rem;
        margin: 0;
        color: var(--text-muted);
    }

    .mobile-loan-card-arrow {
        color: var(--primary-color);
    }

    /* Quick Actions */
    .mobile-quick-actions {
        display: flex;
        justify-content: space-between;
        gap: 0.75rem;
    }

    .quick-action-button {
        flex: 1;
        background: white;
        border-radius: 12px;
        padding: 1rem 0.5rem;
        text-align: center;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: var(--text-color);
    }

    .quick-action-button i {
        font-size: 1.5rem;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .quick-action-button span {
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* Mobile App Navigation Styles - Off-Canvas */
    .mobile-app-nav {
        display: none;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: white;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        padding: 12px 0 8px;
        justify-content: space-around;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .mobile-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: var(--text-muted);
        font-size: 0.75rem;
        padding: 5px 0;
        transition: color 0.2s ease;
        width: 20%;
        text-align: center;
    }

    .nav-icon-container {
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 4px;
    }

    .mobile-nav-item i {
        font-size: 1.25rem;
    }

    .mobile-nav-item span {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        padding: 0 2px;
    }

    .mobile-nav-item.active {
        color: var(--primary-color);
    }

    /* Mobile Menu Toggle Button */
    .mobile-menu-toggle {
        display: none;
        position: fixed;
        top: 15px;
        right: 15px;
        z-index: 1010;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        cursor: pointer;
    }

    /* Off-Canvas Menu */
    .mobile-menu-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1005;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .mobile-menu {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.9);
        width: 90%;
        max-width: 320px;
        max-height: 80vh;
        background-color: white;
        z-index: 1010;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s ease, opacity 0.3s ease;
        padding: 1.5rem;
        overflow-y: auto;
        opacity: 0;
        border-radius: 12px;
    }

    .mobile-menu-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 1rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
        position: relative;
    }

    .mobile-menu-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: var(--text-muted);
        cursor: pointer;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: background-color 0.2s ease;
    }

    .mobile-menu-close:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }

    .mobile-menu-items {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .mobile-menu-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        text-decoration: none;
        color: var(--text-color);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .mobile-menu-item i {
        width: 30px;
        font-size: 1.25rem;
        color: var(--primary-color);
    }

    .mobile-menu-item.active {
        color: var(--primary-color);
        font-weight: 600;
    }

    /* Show mobile menu when active */
    body.mobile-menu-active .mobile-menu {
        display: block;
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    body.mobile-menu-active .mobile-menu-overlay {
        display: block;
        opacity: 1;
    }

    @media (max-width: 768px) {
        .mobile-app-nav {
            display: flex;
        }

        .mobile-menu-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // FAQ Accordion
        const faqItems = document.querySelectorAll('.faq-item');

        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');

            question.addEventListener('click', () => {
                // Close all other items
                faqItems.forEach(otherItem => {
                    if (otherItem !== item && otherItem.classList.contains('active')) {
                        otherItem.classList.remove('active');
                        const toggle = otherItem.querySelector('.faq-toggle i');
                        toggle.className = 'fas fa-plus';
                    }
                });

                // Toggle current item
                item.classList.toggle('active');

                // Update icon
                const toggle = item.querySelector('.faq-toggle i');
                if (item.classList.contains('active')) {
                    toggle.className = 'fas fa-minus';
                } else {
                    toggle.className = 'fas fa-plus';
                }
            });
        });

        // Open first FAQ item by default
        if (faqItems.length > 0) {
            faqItems[0].classList.add('active');
            const toggle = faqItems[0].querySelector('.faq-toggle i');
            toggle.className = 'fas fa-minus';
        }

        // Testimonial Slider
        const indicators = document.querySelectorAll('.testimonial-indicators .indicator');
        const slider = document.querySelector('.testimonials-slider');

        if (indicators.length > 0 && slider) {
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => {
                    // Update active indicator
                    indicators.forEach(ind => ind.classList.remove('active'));
                    indicator.classList.add('active');

                    // Scroll to corresponding testimonial
                    const testimonials = slider.querySelectorAll('.testimonial-card');
                    if (testimonials[index]) {
                        const scrollPosition = testimonials[index].offsetLeft - slider.offsetLeft;
                        slider.scrollTo({
                            left: scrollPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        }
    });

    // Statistics animations
    const statisticCards = document.querySelectorAll('.statistic-card');

    if (statisticCards.length > 0) {
        // Add animation on scroll
        window.addEventListener('scroll', () => {
            statisticCards.forEach(card => {
                const cardPosition = card.getBoundingClientRect().top;
                const screenPosition = window.innerHeight / 1.3;

                if (cardPosition < screenPosition) {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }
            });
        });

        // Initialize cards with animation properties
        statisticCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        });

        // Trigger initial check
        window.dispatchEvent(new Event('scroll'));
    }

    // Loan Calculator
    // Get all the elements
    const loanAmountInput = document.getElementById('loan-amount');
    const loanAmountRange = document.getElementById('loan-amount-range');
    const loanTermInput = document.getElementById('loan-term');
    const loanTermRange = document.getElementById('loan-term-range');
    const interestRateInput = document.getElementById('interest-rate');
    const interestRateRange = document.getElementById('interest-rate-range');
    const loanProductSelect = document.getElementById('loan-product');
    const calculateBtn = document.getElementById('calculate-btn');
    const monthlyPaymentEl = document.getElementById('monthly-payment');
    const totalInterestEl = document.getElementById('total-interest');
    const totalPaymentEl = document.getElementById('total-payment');

    if (loanAmountInput && loanAmountRange && loanTermInput && loanTermRange &&
        interestRateInput && interestRateRange && calculateBtn &&
        monthlyPaymentEl && totalInterestEl && totalPaymentEl) {

        // Sync range and number inputs
        loanAmountInput.addEventListener('input', () => {
            loanAmountRange.value = loanAmountInput.value;
        });

        loanAmountRange.addEventListener('input', () => {
            loanAmountInput.value = loanAmountRange.value;
        });

        loanTermInput.addEventListener('input', () => {
            loanTermRange.value = loanTermInput.value;
        });

        loanTermRange.addEventListener('input', () => {
            loanTermInput.value = loanTermRange.value;
        });

        interestRateInput.addEventListener('input', () => {
            interestRateRange.value = interestRateInput.value;
        });

        interestRateRange.addEventListener('input', () => {
            interestRateInput.value = interestRateRange.value;
        });

        // Handle loan product selection
        if (loanProductSelect) {
            loanProductSelect.addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];

                if (selectedOption.value) {
                    // Get data attributes
                    const minAmount = parseFloat(selectedOption.getAttribute('data-min'));
                    const maxAmount = parseFloat(selectedOption.getAttribute('data-max'));
                    const interestRate = parseFloat(selectedOption.getAttribute('data-rate'));
                    const termMonths = parseInt(selectedOption.getAttribute('data-term'));

                    // Update form values
                    loanAmountInput.min = minAmount;
                    loanAmountInput.max = maxAmount;
                    loanAmountInput.value = minAmount;
                    loanAmountRange.min = minAmount;
                    loanAmountRange.max = maxAmount;
                    loanAmountRange.value = minAmount;

                    interestRateInput.value = interestRate;
                    interestRateRange.value = interestRate;

                    loanTermInput.value = termMonths;
                    loanTermRange.value = termMonths;

                    // Update range values display
                    const rangeValues = loanAmountRange.nextElementSibling;
                    if (rangeValues) {
                        rangeValues.firstElementChild.textContent = '$' + minAmount.toLocaleString();
                        rangeValues.lastElementChild.textContent = '$' + maxAmount.toLocaleString();
                    }

                    // Calculate with new values
                    calculateLoan();
                }
            });
        }

        // Calculate loan
        calculateBtn.addEventListener('click', calculateLoan);

        // Initial calculation
        calculateLoan();

        function calculateLoan() {
            const principal = parseFloat(loanAmountInput.value);
            const interestRate = parseFloat(interestRateInput.value) / 100 / 12;
            const numberOfPayments = parseFloat(loanTermInput.value);

            // Calculate monthly payment
            const x = Math.pow(1 + interestRate, numberOfPayments);
            const monthly = (principal * x * interestRate) / (x - 1);

            if (isFinite(monthly)) {
                const monthlyPayment = monthly.toFixed(2);
                const totalPayment = (monthly * numberOfPayments).toFixed(2);
                const totalInterest = ((monthly * numberOfPayments) - principal).toFixed(2);

                // Update the UI
                monthlyPaymentEl.textContent = '$' + formatNumber(monthlyPayment);
                totalInterestEl.textContent = '$' + formatNumber(totalInterest);
                totalPaymentEl.textContent = '$' + formatNumber(totalPayment);
            }
        }

        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }
    }
</script>

<style>
/* Floating Cards for Hero Section */
.floating-card {
    background: white;
    padding: 12px 16px;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    color: #4f46e5;
    font-size: 16px;
}

.hero-shape-1 {
    position: absolute;
    top: 20%;
    right: -10%;
    animation-delay: 0s;
}

.hero-shape-2 {
    position: absolute;
    bottom: 20%;
    left: -10%;
    animation-delay: 1.5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Hero Image Enhancements */
.hero-image {
    position: relative;
    z-index: 1;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .floating-card {
        font-size: 12px;
        padding: 8px 12px;
    }

    .hero-shape-1,
    .hero-shape-2 {
        display: none;
    }
}
</style>