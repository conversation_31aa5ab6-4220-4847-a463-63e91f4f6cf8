<?php
/**
 * Home Page - Pharaoh Finance Private and Fast Loans
 *
 * This is the main landing page of the loan management system.
 * It provides an overview of services and allows users to get started.
 *
 * @package LendSwift
 */

// Prevent direct access to this file
if (!defined('LENDSWIFT')) {
    die('Direct access to this file is not allowed.');
}

// Get database connection
$db = getDbConnection();

// Get default currency
$default_currency = get_setting('default_currency', 'USD');
$default_currency_symbol = get_setting('default_currency_symbol', '$');
?>

<div class="home-wrapper">
    <!-- Mobile App Navigation (visible only on small screens) -->
    <div class="mobile-app-nav">
        <a href="<?php echo BASE_URL; ?>/" class="mobile-nav-item active">
            <div class="nav-icon-container">
                <i class="fas fa-home"></i>
            </div>
            <span>Home</span>
        </a>
        <a href="<?php echo BASE_URL; ?>/?page=loan-products" class="mobile-nav-item">
            <div class="nav-icon-container">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <span>Loans</span>
        </a>
        <a href="<?php echo BASE_URL; ?>/?page=login" class="mobile-nav-item">
            <div class="nav-icon-container">
                <i class="fas fa-calculator"></i>
            </div>
            <span>Calculate</span>
        </a>
        <a href="<?php echo BASE_URL; ?>/?page=contact" class="mobile-nav-item">
            <div class="nav-icon-container">
                <i class="fas fa-headset"></i>
            </div>
            <span>Support</span>
        </a>
        <a href="<?php echo BASE_URL; ?>/?page=login" class="mobile-nav-item">
            <div class="nav-icon-container">
                <i class="fas fa-user"></i>
            </div>
            <span>Account</span>
        </a>
    </div>

    <!-- Hero Slider Section -->
    <section class="hero-slider">
        <div class="hero-slides">
            <!-- Slide 1 -->
            <div class="hero-slide active">
                <div class="hero-container">
                    <div class="hero-content">
                        <div class="hero-text">
                            <span class="hero-badge">Fast & Secure Private Loans</span>
                            <h1>Financial Solutions <span class="text-gradient">Tailored for You</span></h1>
                            <p class="hero-description">Get the funds you need with Pharaoh Finance Private and Fast Loans. Our streamlined application process offers competitive rates, flexible terms, and exceptional customer service for all your financial needs.</p>
                            <div class="hero-benefits">
                                <div class="benefit-item">
                                    <i class="fas fa-clock"></i>
                                    <span>Quick Approval</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-shield-alt"></i>
                                    <span>Secure Process</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-percentage"></i>
                                    <span>Low Interest Rates</span>
                                </div>
                            </div>
                            <div class="hero-buttons">
                                <a href="<?php echo BASE_URL; ?>/?page=apply" class="button button-primary button-large">Apply Now</a>
                                <a href="<?php echo BASE_URL; ?>/?page=loan-products" class="button button-secondary">View Loan Products</a>
                            </div>
                        </div>
                        <div class="hero-image">
                            <div class="hero-image-container">
                                <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 001.webp" alt="Financial Solutions" class="hero-main-image">
                                <div class="floating-elements">
                                    <div class="floating-card card-1">
                                        <i class="fas fa-dollar-sign"></i>
                                        <span>Fast Funding</span>
                                    </div>
                                    <div class="floating-card card-2">
                                        <i class="fas fa-chart-line"></i>
                                        <span>Growth</span>
                                    </div>
                                    <div class="floating-card card-3">
                                        <i class="fas fa-lock"></i>
                                        <span>Secure</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2 -->
            <div class="hero-slide">
                <div class="hero-container">
                    <div class="hero-content">
                        <div class="hero-text">
                            <span class="hero-badge">Business Growth Solutions</span>
                            <h1>Fuel Your Business <span class="text-gradient">Dreams Today</span></h1>
                            <p class="hero-description">Expand your business with our tailored business loans. From equipment financing to working capital, we provide the funding solutions your business needs to thrive and grow.</p>
                            <div class="hero-benefits">
                                <div class="benefit-item">
                                    <i class="fas fa-chart-line"></i>
                                    <span>Business Growth</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-handshake"></i>
                                    <span>Flexible Terms</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-trophy"></i>
                                    <span>Competitive Rates</span>
                                </div>
                            </div>
                            <div class="hero-buttons">
                                <a href="<?php echo BASE_URL; ?>/?page=apply" class="button button-primary button-large">Apply for Business Loan</a>
                                <a href="<?php echo BASE_URL; ?>/?page=contact" class="button button-secondary">Speak to Expert</a>
                            </div>
                        </div>
                        <div class="hero-image">
                            <div class="hero-image-container">
                                <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 007.webp" alt="Business Solutions" class="hero-main-image">
                                <div class="floating-elements">
                                    <div class="floating-card card-1">
                                        <i class="fas fa-briefcase"></i>
                                        <span>Business Loans</span>
                                    </div>
                                    <div class="floating-card card-2">
                                        <i class="fas fa-rocket"></i>
                                        <span>Fast Growth</span>
                                    </div>
                                    <div class="floating-card card-3">
                                        <i class="fas fa-star"></i>
                                        <span>Expert Support</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3 -->
            <div class="hero-slide">
                <div class="hero-container">
                    <div class="hero-content">
                        <div class="hero-text">
                            <span class="hero-badge">Education Investment</span>
                            <h1>Invest in Your <span class="text-gradient">Future Today</span></h1>
                            <p class="hero-description">Make your educational dreams a reality with our student-friendly loan options. Competitive rates, flexible repayment terms, and dedicated support for your academic journey.</p>
                            <div class="hero-benefits">
                                <div class="benefit-item">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>Education Focus</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Deferred Payments</span>
                                </div>
                                <div class="benefit-item">
                                    <i class="fas fa-heart"></i>
                                    <span>Student Friendly</span>
                                </div>
                            </div>
                            <div class="hero-buttons">
                                <a href="<?php echo BASE_URL; ?>/?page=apply" class="button button-primary button-large">Apply for Education Loan</a>
                                <a href="<?php echo BASE_URL; ?>/?page=loan-products" class="button button-secondary">Learn More</a>
                            </div>
                        </div>
                        <div class="hero-image">
                            <div class="hero-image-container">
                                <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 008.webp" alt="Education Solutions" class="hero-main-image">
                                <div class="floating-elements">
                                    <div class="floating-card card-1">
                                        <i class="fas fa-book"></i>
                                        <span>Education</span>
                                    </div>
                                    <div class="floating-card card-2">
                                        <i class="fas fa-lightbulb"></i>
                                        <span>Knowledge</span>
                                    </div>
                                    <div class="floating-card card-3">
                                        <i class="fas fa-medal"></i>
                                        <span>Success</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slider Navigation -->
        <div class="hero-nav">
            <button class="hero-nav-btn prev" onclick="changeSlide(-1)">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="hero-nav-btn next" onclick="changeSlide(1)">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>

        <!-- Slider Indicators -->
        <div class="hero-indicators">
            <button class="indicator active" onclick="currentSlide(1)"></button>
            <button class="indicator" onclick="currentSlide(2)"></button>
            <button class="indicator" onclick="currentSlide(3)"></button>
        </div>
    </section>

    <!-- Loan Packages Section -->
    <section class="loan-packages-section section-primary-light">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">Flexible Options</span>
                <h2>Our Loan Packages</h2>
                <p>Choose from our comprehensive range of loan products designed to meet your specific financial needs</p>
            </div>

            <div class="loan-packages-grid">
                <?php
                // Get loan products from database
                $loan_products = [];
                try {
                    $products_query = $db->query("
                        SELECT
                            lp.id,
                            lp.name,
                            lp.min_amount,
                            lp.max_amount,
                            lp.interest_rate,
                            lp.term_months,
                            c.symbol as currency_symbol
                        FROM
                            loan_products lp
                        LEFT JOIN
                            currencies c ON lp.currency_id = c.id
                        WHERE
                            lp.status = 'active'
                        LIMIT 3
                    ");

                    if ($products_query && $products_query->num_rows > 0) {
                        while ($product = $products_query->fetch_assoc()) {
                            $loan_products[] = $product;
                        }
                    }
                } catch (Exception $e) {
                    error_log('Error fetching loan products: ' . $e->getMessage());
                }

                // If no products, use sample data
                if (empty($loan_products)) {
                    $loan_products = [
                        [
                            'name' => 'Personal Loan',
                            'min_amount' => 5000,
                            'max_amount' => 50000,
                            'interest_rate' => 8.5,
                            'term_months' => 60,
                            'currency_symbol' => $default_currency_symbol
                        ],
                        [
                            'name' => 'Business Loan',
                            'min_amount' => 10000,
                            'max_amount' => 100000,
                            'interest_rate' => 7.5,
                            'term_months' => 84,
                            'currency_symbol' => $default_currency_symbol
                        ],
                        [
                            'name' => 'Education Loan',
                            'min_amount' => 8000,
                            'max_amount' => 80000,
                            'interest_rate' => 6.5,
                            'term_months' => 72,
                            'currency_symbol' => $default_currency_symbol
                        ]
                    ];
                }

                $package_images = [
                    'demo-image-data/pfploans.com 003.webp',
                    'demo-image-data/pfploans.com 004.webp',
                    'demo-image-data/pfploans.com 005.webp'
                ];

                $package_icons = [
                    'Personal Loan' => 'fa-user',
                    'Business Loan' => 'fa-briefcase',
                    'Education Loan' => 'fa-graduation-cap',
                    'Home Loan' => 'fa-home',
                    'Auto Loan' => 'fa-car'
                ];

                foreach ($loan_products as $index => $product):
                    $icon = $package_icons[$product['name']] ?? 'fa-money-bill-wave';
                    $image = $package_images[$index] ?? $package_images[0];
                    $currency_symbol = $product['currency_symbol'] ?? $default_currency_symbol;
                ?>
                <div class="loan-package-card">
                    <div class="package-image">
                        <img src="<?php echo BASE_URL; ?>/<?php echo $image; ?>" alt="<?php echo htmlspecialchars($product['name']); ?>">
                        <div class="package-overlay">
                            <i class="fas <?php echo $icon; ?>"></i>
                        </div>
                    </div>
                    <div class="package-content">
                        <h3><?php echo htmlspecialchars($product['name']); ?></h3>
                        <div class="package-amount">
                            <span class="amount-range">
                                <?php echo $currency_symbol . number_format($product['min_amount']); ?> -
                                <?php echo $currency_symbol . number_format($product['max_amount']); ?>
                            </span>
                        </div>
                        <div class="package-details">
                            <div class="detail-item">
                                <span class="label">Interest Rate</span>
                                <span class="value"><?php echo $product['interest_rate']; ?>% p.a.</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Term</span>
                                <span class="value">Up to <?php echo $product['term_months']; ?> months</span>
                            </div>
                        </div>
                        <div class="package-features">
                            <ul>
                                <li><i class="fas fa-check"></i> Quick approval process</li>
                                <li><i class="fas fa-check"></i> Flexible repayment terms</li>
                                <li><i class="fas fa-check"></i> Competitive interest rates</li>
                            </ul>
                        </div>
                        <a href="<?php echo BASE_URL; ?>/?page=apply" class="button button-primary button-full">Apply Now</a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Services Overview Section -->
    <section class="services-overview-section section-light">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">Our Services</span>
                <h2>Comprehensive Financial Solutions</h2>
                <p>We provide a complete range of financial services to help you achieve your goals</p>
            </div>

            <div class="services-grid">
                <div class="service-card">
                    <div class="service-image">
                        <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 006.webp" alt="Personal Loans">
                        <div class="service-overlay">
                            <i class="fas fa-user-circle"></i>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3>Personal Loans</h3>
                        <p>Quick and easy personal loans for your immediate financial needs. Whether it's for home improvements, medical expenses, or debt consolidation.</p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> Fast approval in 24 hours</li>
                            <li><i class="fas fa-check"></i> Competitive interest rates</li>
                            <li><i class="fas fa-check"></i> Flexible repayment terms</li>
                        </ul>
                        <a href="<?php echo BASE_URL; ?>/?page=loan-products" class="service-link">Learn More <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-image">
                        <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 007.webp" alt="Business Loans">
                        <div class="service-overlay">
                            <i class="fas fa-briefcase"></i>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3>Business Loans</h3>
                        <p>Fuel your business growth with our tailored business loan solutions. Perfect for expansion, equipment purchase, or working capital needs.</p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> Higher loan amounts</li>
                            <li><i class="fas fa-check"></i> Business-friendly terms</li>
                            <li><i class="fas fa-check"></i> Expert financial advice</li>
                        </ul>
                        <a href="<?php echo BASE_URL; ?>/?page=loan-products" class="service-link">Learn More <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-image">
                        <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 008.webp" alt="Education Loans">
                        <div class="service-overlay">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3>Education Loans</h3>
                        <p>Invest in your future with our education loan programs. Support your academic journey with affordable financing options.</p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> Student-friendly rates</li>
                            <li><i class="fas fa-check"></i> Deferred payment options</li>
                            <li><i class="fas fa-check"></i> No prepayment penalties</li>
                        </ul>
                        <a href="<?php echo BASE_URL; ?>/?page=loan-products" class="service-link">Learn More <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="why-choose-us-section section-darker">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">Why Choose Us</span>
                <h2>Your Trusted Financial Partner</h2>
                <p>Discover why thousands of customers trust Pharaoh Finance Private and Fast Loans for their financial needs</p>
            </div>

            <div class="features-showcase">
                <!-- Feature Cards Grid -->
                <div class="features-cards">
                    <div class="feature-card">
                        <div class="feature-card-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h3>Lightning Fast</h3>
                        <p>24-hour approval and funding process</p>
                        <div class="feature-stat">
                            <span class="stat-number">24hrs</span>
                            <span class="stat-label">Average Processing</span>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-card-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3>Bank-Level Security</h3>
                        <p>256-bit SSL encryption for all transactions</p>
                        <div class="feature-stat">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Secure Platform</span>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-card-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <h3>Competitive Rates</h3>
                        <p>Best-in-market interest rates and terms</p>
                        <div class="feature-stat">
                            <span class="stat-number">5.9%</span>
                            <span class="stat-label">Starting APR</span>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-card-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h3>24/7 Support</h3>
                        <p>Round-the-clock customer assistance</p>
                        <div class="feature-stat">
                            <span class="stat-number">5★</span>
                            <span class="stat-label">Customer Rating</span>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-card-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h3>High Approval</h3>
                        <p>Industry-leading approval rates</p>
                        <div class="feature-stat">
                            <span class="stat-number">98%</span>
                            <span class="stat-label">Approval Rate</span>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-card-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3>Mobile Friendly</h3>
                        <p>Apply and manage loans on any device</p>
                        <div class="feature-stat">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Mobile Optimized</span>
                        </div>
                    </div>
                </div>

                <!-- Main Feature Highlight -->
                <div class="main-feature-highlight">
                    <div class="highlight-content">
                        <div class="highlight-text">
                            <h3>Trusted by Over 50,000+ Customers</h3>
                            <p>Join thousands of satisfied customers who have achieved their financial goals with Pharaoh Finance. Our commitment to excellence and customer satisfaction has made us a leading choice for personal and business loans.</p>
                            <div class="highlight-stats">
                                <div class="highlight-stat">
                                    <span class="highlight-number">$500M+</span>
                                    <span class="highlight-label">Loans Funded</span>
                                </div>
                                <div class="highlight-stat">
                                    <span class="highlight-number">50,000+</span>
                                    <span class="highlight-label">Happy Customers</span>
                                </div>
                                <div class="highlight-stat">
                                    <span class="highlight-number">4.9/5</span>
                                    <span class="highlight-label">Customer Rating</span>
                                </div>
                            </div>
                            <a href="<?php echo BASE_URL; ?>/?page=apply" class="button button-primary button-large">Start Your Application</a>
                        </div>
                        <div class="highlight-image">
                            <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 011.webp" alt="Trusted Financial Partner">
                            <div class="highlight-overlay">
                                <div class="trust-badge">
                                    <i class="fas fa-award"></i>
                                    <span>Trusted Partner</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Application CTA Section -->
    <section class="quick-application-section section-primary-light">
        <div class="container">
            <div class="cta-content">
                <div class="cta-text">
                    <div class="cta-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <h2>Ready to Get Started?</h2>
                    <p>Calculate your loan amount and monthly payments with our easy-to-use loan calculator, then apply for your loan in just a few minutes.</p>
                    <div class="cta-features">
                        <div class="cta-feature">
                            <i class="fas fa-clock"></i>
                            <span>5-minute application</span>
                        </div>
                        <div class="cta-feature">
                            <i class="fas fa-check-circle"></i>
                            <span>Instant pre-approval</span>
                        </div>
                        <div class="cta-feature">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>Same-day funding</span>
                        </div>
                    </div>
                    <div class="cta-buttons">
                        <a href="<?php echo BASE_URL; ?>/?page=apply" class="button button-primary button-large">Apply for Loan</a>
                        <a href="<?php echo BASE_URL; ?>/?page=contact" class="button button-secondary">Contact Us</a>
                    </div>
                </div>
                <div class="cta-image">
                    <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 015.webp" alt="Apply for Loan">
                    <div class="cta-overlay">
                        <div class="calculator-preview">
                            <div class="calc-header">
                                <i class="fas fa-calculator"></i>
                                <span>Loan Calculator</span>
                            </div>
                            <div class="calc-content">
                                <div class="calc-field">
                                    <label>Loan Amount</label>
                                    <span><?php echo $default_currency_symbol; ?>25,000</span>
                                </div>
                                <div class="calc-field">
                                    <label>Monthly Payment</label>
                                    <span><?php echo $default_currency_symbol; ?>485</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Proof Section -->
    <section class="social-proof-section section-light">
        <div class="container">
            <div class="section-header">
                <span class="section-badge">Customer Stories</span>
                <h2>What Our Customers Say</h2>
                <p>Read testimonials from satisfied customers who have achieved their financial goals with our help</p>
            </div>

            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"Pharaoh Finance Private and Fast Loans made my dream of starting a business come true. The application process was incredibly smooth, and I received my funds within 24 hours. Their customer service is exceptional!"</p>
                        <div class="testimonial-author">
                            <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 012.webp" alt="Sarah Johnson">
                            <div class="author-info">
                                <h4>Sarah Johnson</h4>
                                <span>Small Business Owner</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"I needed funds for my daughter's education, and Pharaoh Finance provided the perfect solution. The interest rates were competitive, and the repayment terms were very flexible. Highly recommended!"</p>
                        <div class="testimonial-author">
                            <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 013.webp" alt="Michael Chen">
                            <div class="author-info">
                                <h4>Michael Chen</h4>
                                <span>Teacher</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"The team at Pharaoh Finance went above and beyond to help me consolidate my debts. Their professional approach and transparent communication made the entire process stress-free."</p>
                        <div class="testimonial-author">
                            <img src="<?php echo BASE_URL; ?>/demo-image-data/pfploans.com 014.webp" alt="Emily Rodriguez">
                            <div class="author-info">
                                <h4>Emily Rodriguez</h4>
                                <span>Marketing Manager</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="trust-indicators">
                <div class="trust-item">
                    <div class="trust-number">10,000+</div>
                    <div class="trust-label">Happy Customers</div>
                </div>
                <div class="trust-item">
                    <div class="trust-number">$50M+</div>
                    <div class="trust-label">Loans Funded</div>
                </div>
                <div class="trust-item">
                    <div class="trust-number">4.9/5</div>
                    <div class="trust-label">Customer Rating</div>
                </div>
                <div class="trust-item">
                    <div class="trust-number">24/7</div>
                    <div class="trust-label">Support Available</div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
    /* Home Page Styles */
    .home-wrapper {
        width: 100%;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
    }

    /* Section Background Colors */
    .section-light {
        background-color: #ffffff;
    }

    .section-primary-light {
        background-color: rgba(79, 70, 229, 0.05);
    }

    .section-darker {
        background-color: #f7f9fc;
    }

    /* Container */
    .container {
        width: 100%;
        max-width: 100%;
        padding: 0 4rem;
        box-sizing: border-box;
    }

    /* Section Headers */
    .section-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    .section-badge {
        display: inline-block;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .section-header h2 {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .section-header p {
        font-size: 1.125rem;
        color: var(--text-muted);
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
    }

    /* Hero Slider - Full Width */
    .hero-slider {
        width: 100%;
        position: relative;
        overflow: hidden;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .hero-slides {
        width: 100%;
        position: relative;
    }

    .hero-slide {
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(135deg, #ffffff 0%, rgba(79, 70, 229, 0.05) 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .hero-slide.active {
        opacity: 1;
        transform: translateX(0);
        position: relative;
    }

    .hero-slide.prev {
        transform: translateX(-100%);
    }

    .hero-container {
        width: 100%;
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .hero-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
        min-height: 80vh;
    }

    .hero-text {
        padding-right: 2rem;
    }

    .hero-badge {
        display: inline-block;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-size: 0.875rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
    }

    .hero-text h1 {
        font-size: 3.5rem;
        font-weight: 800;
        color: var(--text-color);
        margin-bottom: 1.5rem;
        line-height: 1.1;
    }

    .text-gradient {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .hero-description {
        font-size: 1.25rem;
        color: var(--text-muted);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .hero-benefits {
        display: flex;
        gap: 2rem;
        margin-bottom: 2.5rem;
        flex-wrap: wrap;
    }

    .benefit-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-color);
        font-weight: 500;
    }

    .benefit-item i {
        color: var(--primary-color);
        font-size: 1.125rem;
    }

    .hero-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.875rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        font-size: 1rem;
    }

    .button-primary {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        color: white;
        box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
    }

    .button-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
    }

    .button-secondary {
        background: white;
        color: var(--primary-color);
        border: 2px solid var(--primary-color);
    }

    .button-secondary:hover {
        background: var(--primary-color);
        color: white;
    }

    .button-large {
        padding: 1rem 2.5rem;
        font-size: 1.125rem;
    }

    .button-full {
        width: 100%;
    }

    /* Hero Image */
    .hero-image {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-left: 2rem;
    }

    .hero-image-container {
        position: relative;
        border-radius: 1rem;
        overflow: hidden;
        width: 100%;
        max-width: 600px;
    }

    .hero-main-image {
        width: 100%;
        height: auto;
        border-radius: 1rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        object-fit: cover;
    }

    .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
    }

    .floating-card {
        position: absolute;
        background: white;
        padding: 1rem;
        border-radius: 0.75rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        animation: float 3s ease-in-out infinite;
    }

    .floating-card i {
        color: var(--primary-color);
        font-size: 1.25rem;
    }

    .floating-card span {
        font-weight: 600;
        color: var(--text-color);
        font-size: 0.875rem;
    }

    .card-1 {
        top: 10%;
        right: -10%;
        animation-delay: 0s;
    }

    .card-2 {
        top: 50%;
        left: -15%;
        animation-delay: 1s;
    }

    .card-3 {
        bottom: 15%;
        right: -5%;
        animation-delay: 2s;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    /* Hero Slider Navigation */
    .hero-nav {
        position: absolute;
        top: 50%;
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 0 2rem;
        pointer-events: none;
        z-index: 10;
    }

    .hero-nav-btn {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        border: none;
        color: var(--primary-color);
        font-size: 1.25rem;
        cursor: pointer;
        transition: all 0.3s ease;
        pointer-events: all;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hero-nav-btn:hover {
        background: var(--primary-color);
        color: white;
        transform: scale(1.1);
    }

    /* Hero Slider Indicators */
    .hero-indicators {
        position: absolute;
        bottom: 2rem;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 1rem;
        z-index: 10;
    }

    .indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.5);
        background: transparent;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .indicator.active {
        background: white;
        border-color: white;
        transform: scale(1.2);
    }

    .indicator:hover {
        border-color: white;
        transform: scale(1.1);
    }

    /* Loan Packages Section */
    .loan-packages-section {
        padding: 5rem 0;
    }

    .loan-packages-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .loan-package-card {
        background: white;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
    }

    .loan-package-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .package-image {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .package-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .loan-package-card:hover .package-image img {
        transform: scale(1.1);
    }

    .package-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(79, 70, 229, 0.9);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .package-content {
        padding: 2rem;
    }

    .package-content h3 {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 1rem;
    }

    .package-amount {
        margin-bottom: 1.5rem;
    }

    .amount-range {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .package-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .detail-item {
        text-align: center;
        padding: 1rem;
        background: rgba(79, 70, 229, 0.05);
        border-radius: 0.5rem;
    }

    .detail-item .label {
        display: block;
        font-size: 0.875rem;
        color: var(--text-muted);
        margin-bottom: 0.25rem;
    }

    .detail-item .value {
        display: block;
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-color);
    }

    .package-features {
        margin-bottom: 2rem;
    }

    .package-features ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .package-features li {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        color: var(--text-muted);
    }

    .package-features li i {
        color: var(--success-color);
        font-size: 0.875rem;
    }

    /* Services Overview Section */
    .services-overview-section {
        padding: 5rem 0;
    }

    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .service-card {
        background: white;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .service-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .service-image {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .service-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .service-card:hover .service-image img {
        transform: scale(1.05);
    }

    .service-overlay {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: rgba(79, 70, 229, 0.9);
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
    }

    .service-content {
        padding: 2rem;
    }

    .service-content h3 {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 1rem;
    }

    .service-content p {
        color: var(--text-muted);
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }

    .service-features {
        list-style: none;
        padding: 0;
        margin: 0 0 1.5rem 0;
    }

    .service-features li {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        color: var(--text-muted);
        font-size: 0.875rem;
    }

    .service-features li i {
        color: var(--success-color);
        font-size: 0.75rem;
    }

    .service-link {
        color: var(--primary-color);
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
    }

    .service-link:hover {
        color: var(--primary-hover);
        transform: translateX(5px);
    }

    /* Why Choose Us Section */
    .why-choose-us-section {
        padding: 5rem 0;
    }

    .features-showcase {
        margin-top: 3rem;
    }

    /* Feature Cards Grid */
    .features-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 4rem;
    }

    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 1rem;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    }

    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .feature-card-icon {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.75rem;
        margin: 0 auto 1.5rem;
    }

    .feature-card h3 {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 1rem;
    }

    .feature-card p {
        color: var(--text-muted);
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }

    .feature-stat {
        text-align: center;
    }

    .feature-stat .stat-number {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.25rem;
    }

    .feature-stat .stat-label {
        font-size: 0.875rem;
        color: var(--text-muted);
    }

    /* Main Feature Highlight */
    .main-feature-highlight {
        background: white;
        border-radius: 1.5rem;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .highlight-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        align-items: center;
        min-height: 400px;
    }

    .highlight-text {
        padding: 3rem;
    }

    .highlight-text h3 {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .highlight-text p {
        color: var(--text-muted);
        margin-bottom: 2rem;
        line-height: 1.6;
        font-size: 1.125rem;
    }

    .highlight-stats {
        display: flex;
        gap: 2rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .highlight-stat {
        text-align: center;
    }

    .highlight-number {
        display: block;
        font-size: 1.75rem;
        font-weight: 800;
        color: var(--primary-color);
        margin-bottom: 0.25rem;
    }

    .highlight-label {
        font-size: 0.875rem;
        color: var(--text-muted);
        font-weight: 500;
    }

    .highlight-image {
        position: relative;
        height: 100%;
        min-height: 400px;
    }

    .highlight-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .highlight-overlay {
        position: absolute;
        top: 2rem;
        right: 2rem;
    }

    .trust-badge {
        background: rgba(255, 255, 255, 0.95);
        padding: 1rem 1.5rem;
        border-radius: 50px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--primary-color);
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .trust-badge i {
        font-size: 1.25rem;
    }
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 1rem;
    }

    .feature-content p {
        font-size: 1.125rem;
        color: var(--text-muted);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .feature-stats {
        display: flex;
        gap: 2rem;
    }

    .stat {
        text-align: center;
    }

    .stat-number {
        display: block;
        font-size: 2rem;
        font-weight: 800;
        color: var(--primary-color);
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--text-muted);
        font-weight: 500;
    }

    /* Quick Application CTA Section */
    .quick-application-section {
        padding: 5rem 0;
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.05) 0%, rgba(79, 70, 229, 0.1) 100%);
    }

    .cta-content {
        display: flex;
        align-items: center;
        gap: 4rem;
    }

    .cta-text {
        flex: 1;
        max-width: 600px;
    }

    .cta-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }

    .cta-text h2 {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 1rem;
    }

    .cta-text p {
        font-size: 1.125rem;
        color: var(--text-muted);
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .cta-features {
        display: flex;
        gap: 2rem;
        margin-bottom: 2.5rem;
        flex-wrap: wrap;
    }

    .cta-feature {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-color);
        font-weight: 500;
    }

    .cta-feature i {
        color: var(--primary-color);
        font-size: 1.125rem;
    }

    .cta-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .cta-image {
        flex: 1;
        position: relative;
        max-width: 500px;
    }

    .cta-image img {
        width: 100%;
        height: auto;
        border-radius: 1rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .cta-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .calculator-preview {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        min-width: 200px;
    }

    .calc-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
        font-weight: 600;
    }

    .calc-content {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .calc-field {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .calc-field label {
        font-size: 0.875rem;
        color: var(--text-muted);
    }

    .calc-field span {
        font-weight: 700;
        color: var(--text-color);
    }

    /* Social Proof Section */
    .social-proof-section {
        padding: 5rem 0;
    }

    .testimonials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
        margin-bottom: 4rem;
    }

    .testimonial-card {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .testimonial-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .stars {
        display: flex;
        gap: 0.25rem;
        margin-bottom: 1rem;
        color: #fbbf24;
    }

    .testimonial-content p {
        color: var(--text-muted);
        line-height: 1.6;
        margin-bottom: 1.5rem;
        font-style: italic;
    }

    .testimonial-author {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .testimonial-author img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
    }

    .author-info h4 {
        font-weight: 600;
        color: var(--text-color);
        margin-bottom: 0.25rem;
    }

    .author-info span {
        font-size: 0.875rem;
        color: var(--text-muted);
    }

    .trust-indicators {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
        padding: 2rem;
        background: rgba(79, 70, 229, 0.05);
        border-radius: 1rem;
    }

    .trust-item {
        text-align: center;
    }

    .trust-number {
        font-size: 2.5rem;
        font-weight: 800;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .trust-label {
        font-size: 1rem;
        color: var(--text-muted);
        font-weight: 500;
    }

    /* Mobile App Navigation */
    .mobile-app-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid var(--border-color);
        display: none;
        z-index: 1000;
        padding: 0.5rem 0;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    }

    .mobile-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: var(--text-muted);
        font-size: 0.75rem;
        padding: 0.5rem;
        transition: all 0.3s ease;
        flex: 1;
    }

    .mobile-nav-item.active,
    .mobile-nav-item:hover {
        color: var(--primary-color);
    }

    .nav-icon-container {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 0.25rem;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .mobile-nav-item.active .nav-icon-container {
        background: var(--primary-light);
    }

    .mobile-nav-item i {
        font-size: 1.125rem;
    }

    /* Responsive Styles */
    @media (max-width: 992px) {
        .container {
            padding: 0 2rem;
        }

        .hero-content,
        .cta-content,
        .feature-item {
            flex-direction: column;
            text-align: center;
            gap: 2rem;
        }

        .feature-item.reverse {
            flex-direction: column;
        }

        .hero-text h1 {
            font-size: 2.5rem;
        }

        .section-header h2 {
            font-size: 2rem;
        }

        .feature-content h3 {
            font-size: 1.5rem;
        }

        .cta-text h2 {
            font-size: 2rem;
        }

        .loan-packages-grid,
        .services-grid,
        .testimonials-grid {
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        }

        .hero-benefits,
        .cta-features,
        .feature-stats {
            justify-content: center;
        }
    }

    /* Responsive Design for Hero Section */
    @media (max-width: 1024px) {
        .hero-container {
            padding: 0 1.5rem;
        }

        .hero-content {
            gap: 3rem;
        }

        .hero-text {
            padding-right: 1rem;
        }

        .hero-image {
            padding-left: 1rem;
        }
    }

    @media (max-width: 768px) {
        .container {
            padding: 0 1rem;
        }

        /* Hero Slider Mobile */
        .hero-slider {
            min-height: auto;
            padding: 2rem 0;
        }

        .hero-slide {
            min-height: auto;
            padding: 2rem 0;
        }

        .hero-container {
            padding: 0 1rem;
        }

        .hero-content {
            grid-template-columns: 1fr;
            gap: 2rem;
            text-align: center;
            min-height: auto;
        }

        .hero-text {
            padding-right: 0;
            order: 1;
        }

        .hero-image {
            padding-left: 0;
            order: 2;
        }

        .hero-image-container {
            max-width: 400px;
            margin: 0 auto;
        }

        .hero-nav {
            padding: 0 1rem;
        }

        .hero-nav-btn {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        /* Feature Cards Mobile */
        .features-cards {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .highlight-content {
            grid-template-columns: 1fr;
        }

        .highlight-text {
            padding: 2rem;
        }

        .highlight-text h3 {
            font-size: 1.5rem;
        }

        .highlight-stats {
            justify-content: center;
            gap: 1.5rem;
        }

        .highlight-image {
            min-height: 300px;
        }

        .mobile-app-nav {
            display: flex;
        }

        body {
            padding-bottom: 70px;
        }

        .hero-text h1 {
            font-size: 2rem;
        }

        .section-header h2 {
            font-size: 1.75rem;
        }

        .hero-description,
        .cta-text p {
            font-size: 1rem;
        }

        .loan-packages-grid,
        .services-grid,
        .testimonials-grid {
            grid-template-columns: 1fr;
        }

        .trust-indicators {
            grid-template-columns: repeat(2, 1fr);
        }

        .hero-benefits,
        .cta-features {
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .hero-buttons,
        .cta-buttons {
            flex-direction: column;
            width: 100%;
        }

        .button {
            width: 100%;
            justify-content: center;
        }

        .floating-card {
            display: none;
        }

        .calculator-preview {
            position: static;
            transform: none;
            margin-top: 1rem;
        }

        .cta-overlay {
            position: static;
            transform: none;
        }

        /* Mobile app-like styles */
        .section-light, .section-darker, .section-primary-light {
            border-radius: 20px;
            margin: 0 10px 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .button-primary {
            border-radius: 50px;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .button-secondary {
            border-radius: 50px;
        }
    }

    @media (max-width: 480px) {
        .hero-text h1 {
            font-size: 1.75rem;
        }

        .section-header h2 {
            font-size: 1.5rem;
        }

        .trust-indicators {
            grid-template-columns: 1fr;
        }

        .trust-number {
            font-size: 2rem;
        }

        .feature-stats {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>

<script>
    // Hero Slider Functionality
    let currentSlideIndex = 0;
    const slides = document.querySelectorAll('.hero-slide');
    const indicators = document.querySelectorAll('.indicator');
    const totalSlides = slides.length;

    function showSlide(index) {
        // Remove active class from all slides and indicators
        slides.forEach(slide => slide.classList.remove('active', 'prev'));
        indicators.forEach(indicator => indicator.classList.remove('active'));

        // Add active class to current slide and indicator
        slides[index].classList.add('active');
        indicators[index].classList.add('active');

        currentSlideIndex = index;
    }

    function nextSlide() {
        const nextIndex = (currentSlideIndex + 1) % totalSlides;
        showSlide(nextIndex);
    }

    function prevSlide() {
        const prevIndex = (currentSlideIndex - 1 + totalSlides) % totalSlides;
        showSlide(prevIndex);
    }

    function changeSlide(direction) {
        if (direction === 1) {
            nextSlide();
        } else {
            prevSlide();
        }
    }

    function currentSlide(index) {
        showSlide(index - 1);
    }

    // Auto-play slider
    let autoPlayInterval = setInterval(nextSlide, 6000);

    // Pause auto-play on hover
    const heroSlider = document.querySelector('.hero-slider');
    if (heroSlider) {
        heroSlider.addEventListener('mouseenter', () => {
            clearInterval(autoPlayInterval);
        });

        heroSlider.addEventListener('mouseleave', () => {
            autoPlayInterval = setInterval(nextSlide, 6000);
        });
    }

    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    heroSlider.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
    });

    heroSlider.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                nextSlide(); // Swipe left - next slide
            } else {
                prevSlide(); // Swipe right - previous slide
            }
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Initialize slider
        showSlide(0);

        // Add smooth scrolling animation to floating cards
        const floatingCards = document.querySelectorAll('.floating-card');
        floatingCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.5}s`;
        });

        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all cards and sections
        const animatedElements = document.querySelectorAll('.loan-package-card, .service-card, .testimonial-card, .feature-card');
        animatedElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });

        // Add hover effects for mobile
        if (window.innerWidth <= 768) {
            const cards = document.querySelectorAll('.loan-package-card, .service-card, .testimonial-card, .feature-card');
            cards.forEach(card => {
                card.addEventListener('touchstart', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                card.addEventListener('touchend', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        }
    });
</script>